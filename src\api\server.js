import request from '@/utils/request'


export function region_list(data) {
  return request({
    url: '/region/list',
    method: 'post',
    data
  })
}

export function region_edit(data) {
  return request({
    url: '/region/edit',
    method: 'post',
    data
  })
}

export function region_del(data) {
  return request({
    url: '/region/del',
    method: 'post',
    data
  })
}

export function region_clear(data) {
  return request({
    url: '/region/clear',
    method: 'post',
    data
  })
}

export function region_changes(data) {
  return request({
    url: '/region/changes',
    method: 'post',
    data
  })
}

export function region_changes_edit(data) {
  return request({
    url: '/region/changes_edit',
    method: 'post',
    data
  })
}
export function region_changes_del(data) {
  return request({
    url: '/region/changes_del',
    method: 'post',
    data
  })
}

export function region_changes_beats(data) {
  return request({
    url: '/region/changes_beats',
    method: 'post',
    data
  })
}

export function region_changes_echarts(data) {
  return request({
    url: '/region/changes_echarts',
    method: 'post',
    data
  })
}


export function server_list(data) {
  return request({
    url: '/server/list',
    method: 'post',
    data
  })
}

export function server_edit(data) {
  return request({
    url: '/server/edit',
    method: 'post',
    data
  })
}

export function server_del(data) {
  return request({
    url: '/server/del',
    method: 'post',
    data
  })
}
export function server_check(data) {
  return request({
    url: '/server/check',
    method: 'post',
    data
  })
}

export function server_log(data) {
  return request({
    url: '/server/log',
    method: 'post',
    data
  })
}

export function server_echarts(data) {
  return request({
    url: '/server/echarts',
    method: 'post',
    data
  })
}

export function server_beats(data) {
  return request({
    url: '/server/beats',
    method: 'post',
    data
  })
}
