<template>
  <div class="app-container">
    <!-- iOS订单卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">iOS订单</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="账号">
            <el-input v-model="searchForm.member_account" placeholder="请输入账号" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="notificationType">
            <el-input v-model="searchForm.notificationType" placeholder="请输入notificationType" clearable></el-input>
          </el-form-item>
          <el-form-item label="subtype">
            <el-input v-model="searchForm.subtype" placeholder="请输入subtype" clearable></el-input>
          </el-form-item>
          <el-form-item label="登录时间">
            <el-date-picker
              v-model="searchForm.ct_start"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 140px;"
            ></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="searchForm.ct_end"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 140px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- iOS订单列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="iosOrderList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
           height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="member_id" label="用户ID" width="100" align="center"></el-table-column>
          <el-table-column prop="member_account" label="账号" width="140" align="center"></el-table-column>
          <el-table-column prop="notificationType" label="notificationType" width="160" align="center"></el-table-column>
          <el-table-column prop="subtype" label="subtype" width="100" align="center"></el-table-column>
          <el-table-column label="response" width="120" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="showJsonDialog(scope.row.response)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
          <el-table-column prop="status" label="状态" width="160" align="center">
            <template slot-scope="scope">
              <el-tag>{{ formatStatus(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="160" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- JSON查看对话框 -->
    <el-dialog :visible.sync="dialogVisible" title="response JSON" width="600px" :close-on-click-modal="false">
      <pre style="max-height: 400px;overflow:auto;background:#f6f6f6;padding:12px;border-radius:4px;">{{ dialogJson }}</pre>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { member_order_ios } from '@/api/member'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  member_account: '',
  status: '',
  notificationType: '',
  subtype: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const iosOrderList = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '自动续订订阅已激活', value: '1' },
  { label: '自动续订订阅已过期', value: '2' },
  { label: '自动续订订阅处于计费重试期', value: '3' },
  { label: '自动续订订阅处于计费宽限期', value: '4' },
  { label: '自动续订订阅已撤销', value: '5' },
  { label: '用户未找到', value: '9' },
  { label: '异常-10', value: '10' },
  { label: '异常-11', value: '11' }
]

// JSON对话框
const dialogVisible = ref(false)
const dialogJson = ref('')

// 获取iOS订单列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await member_order_ios(params)
    iosOrderList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取iOS订单列表失败', error)
  } finally {
    loading.value = false
  }
}

// 格式化状态
const formatStatus = (val) => {
  switch (val) {
    case '1': return '自动续订订阅已激活'
    case '2': return '自动续订订阅已过期'
    case '3': return '自动续订订阅处于计费重试期'
    case '4': return '自动续订订阅处于计费宽限期'
    case '5': return '自动续订订阅已撤销'
    case '9': return '用户未找到'
    case '10': return '异常-10'
    case '11': return '异常-11'
    default: return '未知'
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 显示JSON对话框
const showJsonDialog = (jsonStr) => {
  try {
    dialogJson.value = JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    dialogJson.value = jsonStr
  }
  dialogVisible.value = true
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
