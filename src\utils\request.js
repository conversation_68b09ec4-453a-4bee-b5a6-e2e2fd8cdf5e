import axios from "axios";
import { Message } from "element-ui";
import store from "@/store";
import { getToken } from "@/utils/auth";
import { globalParams } from "@/utils/index";
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // baseURL: 'https://channel.softapi.cn/admin', // url = base url + request url
  //baseURL: 'http://127.0.0.1:9522/admin',

  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 6000000000, // request timeout
  headers: {
    "Content-Type": "multipart/form-data",
  },
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    // console.log(config.data)
    if (config.data === undefined) {
      config.data = globalParams({});
    } else {
      config.data = globalParams(config.data);
    }
    if (store.getters.token) {
      config.headers["token"] = getToken();
    }
    return config;
  },
  (error) => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.code === 200) {
      return res
    } else if(res.code === 401){
      store.dispatch('user/logout')
      Message.error(res.msg);
      return Promise.reject(res)
    }else{
      Message.error(res.msg);
      return Promise.reject(res)
    }
  },
  (error) => {
    console.log("err" + error); // for debug
    Message({
      message: error.message,
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
