// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// 改写样式

.el-dialog {
  margin-top: unset !important;
  position: absolute;
  // background: #212121;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  border: 2px solid #5865f2;
  .el-dialog__title {
    // color: #fff;
  }
  .el-dialog__body {
    padding: 10px 20px;
    max-height: 80vh;
    overflow: auto;
  }
}

.svg-world-icon {
  width: 30px;
  height: 30px;
}

.el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

// 全局调大弹窗关闭按钮
.el-dialog__close {
  font-size: 24px !important;
  font-weight: bold;
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.el-dialog__headerbtn {
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
}
