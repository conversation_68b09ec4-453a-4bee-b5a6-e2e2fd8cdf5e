<template>
  <div class="app-container">
    <!-- 版本配置卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">版本配置</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddVersion">添加版本配置</el-button>
        </div>
      </div>

      <!-- 版本配置列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="versionList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" align="center" width="80"></el-table-column>
          <el-table-column prop="version" label="版本号" align="center" width="100"></el-table-column>
          <el-table-column prop="os" label="操作系统" align="center" width="120"></el-table-column>
          <el-table-column label="强制更新" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.force === 2 ? 'danger' : 'info'">
                {{ scope.row.force === 2 ? '强制更新' : '不强制' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="downurl" label="下载地址" align="center">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.downurl" placement="top" :show-after="500">
                <el-link type="primary" :href="scope.row.downurl" target="_blank">
                  {{ scope.row.downurl ? (scope.row.downurl.length > 30 ? scope.row.downurl.substring(0, 30) + '...' : scope.row.downurl) : '-' }}
                </el-link>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="filemd5" label="文件MD5" align="center" width="180">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.filemd5" placement="top" :show-after="500">
                <span>{{ scope.row.filemd5 ? (scope.row.filemd5.length > 15 ? scope.row.filemd5.substring(0, 15) + '...' : scope.row.filemd5) : '-' }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="force_ids" label="版本号ID集合" align="center" width="150">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.force_ids" placement="top" :show-after="500">
                <span>{{ scope.row.force_ids || '-' }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="最后更新时间" align="center" width="160">
            <template slot-scope="scope">
              {{ formatTime(scope.row.last_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditVersion(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 版本编辑对话框 -->
    <el-dialog
      :title="versionDialogTitle"
      :visible.sync="versionDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="versionFormRef"
        :model="versionForm"
        :rules="versionFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="操作系统" prop="os">
          <el-select v-model="versionForm.os" placeholder="请选择操作系统">
            <el-option
              v-for="item in osOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="versionForm.version" placeholder="请输入版本号"></el-input>
        </el-form-item>
        <el-form-item label="下载地址" prop="downurl">
          <el-input v-model="versionForm.downurl" placeholder="请输入下载地址"></el-input>
        </el-form-item>
        <el-form-item label="文件MD5" prop="filemd5">
          <el-input v-model="versionForm.filemd5" placeholder="请输入文件MD5"></el-input>
        </el-form-item>
        <el-form-item label="版本号ID集合" prop="force_ids">
          <el-input v-model="versionForm.force_ids" placeholder="请输入版本号ID集合"></el-input>
          <div class="form-tip">多个ID请使用逗号分隔</div>
        </el-form-item>
        <el-form-item label="强制更新" prop="force">
          <el-radio-group v-model="versionForm.force">
            <el-radio :label="1">不强制</el-radio>
            <el-radio :label="2">强制更新</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <div class="editor-container">
            <quill-editor
              v-model="versionForm.remark"
              ref="quillEditor"
              :options="editorOption"
              @blur="onEditorBlur($event)"
              @focus="onEditorFocus($event)"
              @ready="onEditorReady($event)"
            ></quill-editor>
          </div>
          <div class="form-tip">支持HTML格式</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="versionDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitVersionForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { version_list, version_edit } from '@/api/sys'
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import he from 'he'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义表格数据
const versionList = ref([])
const loading = ref(false)

// 版本表单对话框
const versionDialogVisible = ref(false)
const versionDialogTitle = ref('')
const versionForm = reactive({
  vid: 0,
  downurl: '',
  filemd5: '',
  force_ids: '',
  force: 1,
  os: '',
  version: '',
  remark: ''
})

// 操作系统选项
const osOptions = [
  { value: "windows", label: "Windows" },
  { value: "mac", label: "Mac" },
  { value: "linux", label: "Linux" },
  { value: "android", label: "Android" },
  { value: "ios", label: "IOS" }
]

// 富文本编辑器配置
const editorOption = {
  placeholder: '请输入备注内容...',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link'],
      ['clean']
    ]
  }
}

// 表单验证规则
const versionFormRules = {
  os: [
    { required: true, message: '请选择操作系统', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  downurl: [
    { required: true, message: '请输入下载地址', trigger: 'blur' }
  ],
  force: [
    { required: true, message: '请选择是否强制更新', trigger: 'change' }
  ]
}

// 版本表单ref
const versionFormRef = ref(null)
const submitLoading = ref(false)

// 获取版本列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {}
    const res = await version_list(params)
    if (res.code === 200) {
      versionList.value = res.data.lists
    }
  } catch (error) {
    console.error('Error fetching versions:', error)
  } finally {
    loading.value = false
  }
}

// 获取操作系统名称
const getOsName = (osCode) => {
  const os = osOptions.find(item => item.value === osCode)
  return os ? os.label : osCode
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 打开添加版本对话框
const handleAddVersion = () => {
  versionDialogTitle.value = '新增版本配置'
  // 重置表单
  Object.keys(versionForm).forEach(key => {
    if (key === 'vid') {
      versionForm[key] = 0
    } else if (key === 'force') {
      versionForm[key] = 1
    } else {
      versionForm[key] = ''
    }
  })
  versionDialogVisible.value = true
}

// 打开编辑版本对话框
const handleEditVersion = (row) => {
  versionDialogTitle.value = '编辑版本配置'
  // 填充表单数据
  versionForm.vid = row.id || 0
  versionForm.downurl = row.downurl || ''
  versionForm.filemd5 = row.filemd5 || ''
  versionForm.force_ids = row.force_ids || ''
  versionForm.force = row.force || 1
  versionForm.os = row.os || ''
  versionForm.version = row.version || ''
  versionForm.remark = he.decode(row.remark || '')

  versionDialogVisible.value = true
}

// 提交版本表单
const submitVersionForm = async () => {
  if (!versionFormRef.value) return

  versionFormRef.value.validate(async (valid) => {
    if (!valid) {
      return false
    }

    submitLoading.value = true
    try {
      const params = { ...versionForm }
      const res = await version_edit(params)
      if (res.code === 200) {
        versionDialogVisible.value = false
        getList() // 刷新列表
      }
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      submitLoading.value = false
    }
  })
}

// 编辑器事件处理
const onEditorBlur = (quill) => {
  console.log('编辑器失去焦点:', quill)
}

const onEditorFocus = (quill) => {
  console.log('编辑器获得焦点:', quill)
}

const onEditorReady = (quill) => {
  console.log('编辑器初始化完成:', quill)
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式

/* 富文本编辑器相关样式 */
.editor-container {
  width: 100%;
  margin-bottom: 5px;
}

.editor-container >>> .ql-container {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.editor-container >>> .ql-snow .ql-tooltip {
  z-index: 9999;
}
</style>
