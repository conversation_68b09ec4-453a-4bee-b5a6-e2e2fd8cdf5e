import request from '@/utils/request'


export function login(data) {
  return request({
    url: '/login/login',
    method: 'post',
    data
  })
}

export function bindGoogle(data) {
  return request({
    url: '/login/google',
    method: 'post',
    data
  })
}

export function logout(data) {
  return request({
    url: `/login/out`,
    method: 'post',
    data
  })
}

export function master_pass(data) {
  return request({
    url: `/master/pass`,
    method: 'post',
    data
  })
}
