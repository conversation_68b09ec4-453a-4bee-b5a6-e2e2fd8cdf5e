// Node.js 脚本用于重新组织 src/views 目录结构
const fs = require('fs');
const path = require('path');

// 文件移动映射表
const fileMoves = [
  // 首页
  { from: 'src/views/home/<USER>', to: 'src/views/home.vue' },
  
  // 系统管理模块
  { from: 'src/views/sys/role/index.vue', to: 'src/views/sys/role.vue' },
  { from: 'src/views/sys/menu/index.vue', to: 'src/views/sys/menu.vue' },
  { from: 'src/views/sys/log/index.vue', to: 'src/views/sys/log.vue' },
  { from: 'src/views/sys/email/index.vue', to: 'src/views/sys/email.vue' },
  { from: 'src/views/sys/order/index.vue', to: 'src/views/sys/order.vue' },
  { from: 'src/views/sys/version/index.vue', to: 'src/views/sys/version.vue' },
  { from: 'src/views/sys/config/index.vue', to: 'src/views/sys/config.vue' },
  
  // 用户管理模块
  { from: 'src/views/member/list/index.vue', to: 'src/views/member/list.vue' },
  { from: 'src/views/member/order/index.vue', to: 'src/views/member/order.vue' },
  { from: 'src/views/member/trial/index.vue', to: 'src/views/member/trial.vue' },
  { from: 'src/views/member/ios/index.vue', to: 'src/views/member/ios.vue' },
  { from: 'src/views/member/log/index.vue', to: 'src/views/member/log.vue' },
  
  // 渠道商管理模块
  { from: 'src/views/channel/list/index.vue', to: 'src/views/channel/list.vue' },
  { from: 'src/views/channel/code/index.vue', to: 'src/views/channel/code.vue' },
  { from: 'src/views/channel/log/index.vue', to: 'src/views/channel/log.vue' },
  
  // 地区管理模块
  { from: 'src/views/region/list/index.vue', to: 'src/views/region/list.vue' },
  { from: 'src/views/region/changes/index.vue', to: 'src/views/region/changes.vue' },
  { from: 'src/views/region/beats/index.vue', to: 'src/views/region/beats.vue' },
  
  // 服务器管理模块
  { from: 'src/views/server/list/index.vue', to: 'src/views/server/list.vue' },
  { from: 'src/views/server/beats/index.vue', to: 'src/views/server/beats.vue' },
  { from: 'src/views/server/log/index.vue', to: 'src/views/server/log.vue' },
  
  // 错误页面
  { from: 'src/views/404/index.vue', to: 'src/views/404.vue' }
];

// 需要删除的空目录
const dirsToRemove = [
  'src/views/sys/admin',
  'src/views/sys/role', 
  'src/views/sys/menu',
  'src/views/sys/log',
  'src/views/sys/email',
  'src/views/sys/order',
  'src/views/sys/version',
  'src/views/sys/config',
  'src/views/member/list',
  'src/views/member/order',
  'src/views/member/trial',
  'src/views/member/ios',
  'src/views/member/log',
  'src/views/channel/list',
  'src/views/channel/code',
  'src/views/channel/log',
  'src/views/region/list',
  'src/views/region/changes',
  'src/views/region/beats',
  'src/views/server/list',
  'src/views/server/beats',
  'src/views/server/log',
  'src/views/home',
  'src/views/404'
];

function moveFile(from, to) {
  try {
    if (fs.existsSync(from)) {
      const content = fs.readFileSync(from, 'utf8');
      fs.writeFileSync(to, content);
      console.log(`✅ 移动: ${from} -> ${to}`);
      return true;
    } else {
      console.log(`⚠️  源文件不存在: ${from}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 移动失败: ${from} -> ${to}`, error.message);
    return false;
  }
}

function removeDirectory(dir) {
  try {
    if (fs.existsSync(dir)) {
      const stats = fs.statSync(dir);
      if (stats.isDirectory()) {
        const files = fs.readdirSync(dir);
        if (files.length === 0) {
          fs.rmdirSync(dir);
          console.log(`🗑️  删除空目录: ${dir}`);
        } else {
          console.log(`⚠️  目录不为空，跳过: ${dir}`);
        }
      }
    }
  } catch (error) {
    console.error(`❌ 删除目录失败: ${dir}`, error.message);
  }
}

function main() {
  console.log('🚀 开始重新组织 src/views 目录结构...\n');
  
  // 第一步：移动文件
  console.log('📁 第一步：移动文件');
  let successCount = 0;
  fileMoves.forEach(move => {
    if (moveFile(move.from, move.to)) {
      successCount++;
    }
  });
  
  console.log(`\n✅ 文件移动完成: ${successCount}/${fileMoves.length} 个文件成功移动\n`);
  
  // 第二步：删除空目录
  console.log('🗑️  第二步：删除空目录');
  dirsToRemove.forEach(dir => {
    removeDirectory(dir);
  });
  
  console.log('\n🎉 目录结构重新组织完成！');
  console.log('\n📋 新的目录结构：');
  console.log('src/views/');
  console.log('├── home.vue');
  console.log('├── sys/');
  console.log('│   ├── admin.vue');
  console.log('│   ├── role.vue');
  console.log('│   ├── menu.vue');
  console.log('│   ├── log.vue');
  console.log('│   ├── email.vue');
  console.log('│   ├── order.vue');
  console.log('│   ├── version.vue');
  console.log('│   └── config.vue');
  console.log('├── member/');
  console.log('│   ├── list.vue');
  console.log('│   ├── order.vue');
  console.log('│   ├── trial.vue');
  console.log('│   ├── ios.vue');
  console.log('│   └── log.vue');
  console.log('├── channel/');
  console.log('│   ├── list.vue');
  console.log('│   ├── code.vue');
  console.log('│   └── log.vue');
  console.log('├── region/');
  console.log('│   ├── list.vue');
  console.log('│   ├── changes.vue');
  console.log('│   └── beats.vue');
  console.log('├── server/');
  console.log('│   ├── list.vue');
  console.log('│   ├── beats.vue');
  console.log('│   └── log.vue');
  console.log('├── login/');
  console.log('│   ├── index.vue');
  console.log('│   └── components/');
  console.log('└── 404.vue');
}

if (require.main === module) {
  main();
}

module.exports = { moveFile, removeDirectory, fileMoves, dirsToRemove };
