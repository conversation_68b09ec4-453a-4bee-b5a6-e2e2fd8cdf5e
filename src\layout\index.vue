<template>
    <div class="app-wrap">
        <div class="left-menu q-px-sm row column items-center">
            <div class="logo">
                <img src="@/assets/imgs/logo.png" alt="">
                <div class="text-subtitle1">Mjsurf-Admin</div>
            </div>
            <el-menu :default-active="$route.fullPath" class="el-menu-vertical-demo">
                <menuItem v-for="route in permissionRoutes" :key="route.path" :item="route"/>
            </el-menu>
        </div>
        <div class="app-content">
            <div class="top-fixed">
                <el-dropdown>
                    <div class="user-wrap">
                        <div class="user">
                            <img src="@/assets/imgs/logo.png" alt=""/>
                            <div>
                                <b>{{ userInfo.account }}</b>
                                <div>{{ userInfo.name }}</div>
                            </div>
                        </div>
                        <i class="el-icon-arrow-down"></i>
                    </div>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native="changePwd">修改密码</el-dropdown-item>
                        <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <div class="main-content">
                <router-view></router-view>
            </div>
        </div>
        <ChangePwd ref="changePwd"/>
    </div>
</template>

<script>
import menuItem from './components/menuItem.vue'
import ChangePwd from '@/components/ChangePwd'
import {mapGetters} from "vuex";

export default {
    components: {
        menuItem,
        ChangePwd
    },
    data() {
        return {}
    },
    computed: {
        ...mapGetters(["userInfo", "permissionRoutes"]),
    },
    methods: {
        changePwd() {
            this.$refs.changePwd.open()
        },
        logout() {
            this.$confirm('确定要退出登录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$store.dispatch("user/logout");
            }).catch(() => {
                // 用户取消退出
            });
        }
    },
};
</script>

<style lang="scss" scoped>

@keyframes checkeding-animation {
    0% {
        height: 18px;
    }
    50% {
        height: 27px;
    }

    100% {
        height: 36px;
    }
}

.app-wrap {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;

    .app-content {
        flex: 1;
        overflow: hidden;

        .top-fixed {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 0 40px;
            position: relative;
            box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

            .refresh-btn {
                padding: 10px;
                font-size: 22px;
                margin-right: 20px;
            }

            .user-wrap {
                padding: 10px;
                border-radius: 6px;
                display: flex;
                align-items: center;
                cursor: pointer;

                &:hover {
                    background: rgb(239, 242, 254);
                }

                .user {
                    display: flex;
                    align-items: center;
                    margin-right: 40px;

                    img {
                        width: 28px;
                    }

                    > div {
                        margin-left: 20px;
                    }
                }

                i {
                    font-size: 18px;
                }
            }
        }

        .main-content {
            width: 100%;
            height: calc(100vh - 60px);
            padding: 10px;
            // background: #fafbfc;
            > div {
                height: 100%;
                padding: 10px;
            }
        }
    }
}

.left-menu {
    width: 210px;
    border-right: 1px solid rgba(0, 21, 41, 0.08);
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;

    .logo {
        height: 60px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        position: relative;

        img {
            width: 30px;
            margin-right: 10px;
        }

        .refresh-btn {
            position: absolute;
            right: 5px;
            padding: 5px;
            font-size: 18px;
        }
    }

    .el-menu-vertical-demo {
        flex: 1;
        width: 100%;
        background: transparent;
        border-right: unset;
        overflow: auto;
        overflow-x: hidden;
    }
}

</style>
