import Vue from 'vue'

import 'normalize.css/normalize.css'
import ElementUI from 'element-ui';
import '@/styles/element-variables.scss'
import '@/styles/index.scss'
import '@/assets/iconfont/iconfont.css'
import '@/assets/iconfont/iconfont.js'


import App from './App.vue'
import router from './router'
import store from './store'

import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
VMdPreview.use(githubTheme);


// import './icons'
import './permission'

// import Vconsole from 'vconsole';
// const vConsole = new Vconsole();

import './assets/worldfont/iconfont.js'

import WorldIcon from '@/components/SvgIcon/index.vue'

Vue.use(ElementUI);
Vue.use(VMdPreview);
Vue.component('world-icon', WorldIcon)

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
