import router from './router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import {getToken, getUserInfo} from '@/utils/auth' // get token from cookie
import store from './store'

NProgress.configure({showSpinner: false}) // NProgress Configuration

// 白名单路径（包含根路径和404页面）
const whiteList = ['/', '/404'] // no redirect whitelist

// 标记是否已经添加了动态路由
let hasAddRoutes = false

// 检查路径是否在菜单列表中
function checkPath(path, menuList) {
  for (const menu of menuList) {
    if (menu.show_action === path) {
      return true
    }
    // 检查 menu 子菜单
    if (menu.menu && menu.menu.length > 0) {
      if (checkPath(path, menu.menu)) return true
    }

    // 检查 button 按钮/接口
    if (menu.button && menu.button.length > 0) {
      if (checkPath(path, menu.button)) return true
    }
  }
  return false
}

// 获取第一个菜单路径
function getFirstMenuPath(menuList) {
  if (!menuList || menuList.length === 0) return '/home'
  const firstMenu = menuList[0]
  if (firstMenu.type === 1 && firstMenu.menu && firstMenu.menu.length > 0) {
    return firstMenu.menu[0].show_action
  }
  return firstMenu.show_action
}

// 检查是否为有效路径
function isValidPath(path) {
  // 白名单路径都是有效的
  return whiteList.includes(path)
}

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const hasToken = getToken()

  // 对于已登录用户，允许所有以密钥开头的路径通过初始检查
  // 对于未登录用户，进行严格的路径检查
  if (!hasToken && !isValidPath(to.path)) {
    next('/404')
    NProgress.done()
    return
  }

  if (hasToken) {
    if (to.path === '/') {
      // 已登录用户访问根路径，跳转到第一个菜单页面
      const userInfo = getUserInfo()
      if (userInfo && userInfo.menuList) {
        const firstPath = getFirstMenuPath(userInfo.menuList)
        next({path: firstPath})
      } else {
        // 如果没有菜单权限，重新获取用户信息
        next()
      }
      NProgress.done()
    } else {
      if (!hasAddRoutes) {
        try {
          // 从localStorage获取用户信息
          const userInfo = getUserInfo()
          if (userInfo) {
            store.commit('user/setUserInfo', userInfo)
            // 生成动态路由
            store.commit('user/getPermissionRoutes')
            // 添加动态路由
            const accessRoutes = store.getters.permissionRoutes
            router.addRoutes(accessRoutes)
            // 标记路由已添加
            hasAddRoutes = true
            // 检查当前路径是否在菜单列表中（根路径除外）
            if (to.path !== '/' && !checkPath(to.path, userInfo.menuList)) {
              // 如果路径不在菜单列表中，跳转到第一个菜单页面
              const firstPath = getFirstMenuPath(userInfo.menuList)
              next(firstPath)
              NProgress.done()
              return
            }
            // 重新匹配路由
            next({...to, replace: true})
          } else {
            // 如果没有用户信息，跳转到登录页
            await store.dispatch('user/logout')
            next(`/login?redirect=${to.path}`)
            NProgress.done()
          }
        } catch (error) {
          // 如果处理失败，清除token并跳转到登录页
          await store.dispatch('user/logout')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      } else {
        // 如果路由已添加，检查当前路由是否存在
        if (to.matched.length === 0) {
          // 路由不存在，可能是刷新页面导致动态路由丢失
          // 重置路由添加标记，重新走完整的路由添加流程
          hasAddRoutes = false

          // 重新导航，这次会进入 !hasAddRoutes 分支
          next({...to, replace: true})
        } else {
          next()
        }
      }
    }
  } else {
    // 未登录用户
    if (to.path === '/') {
      // 未登录用户访问根路径，显示登录页面
      next()
    } else if (whiteList.indexOf(to.path) !== -1) {
      // 白名单路径允许访问
      next()
    } else {
      // 其他路径需要登录，重定向到根路径并带上redirect参数
      next(`/?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

// 添加全局路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  // 如果是路由匹配失败，重定向到404
  if (error.name === 'NavigationDuplicated' || error.message.includes('Avoided redundant navigation')) {
    // 忽略重复导航错误
    return
  }
  router.push('/404')
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
