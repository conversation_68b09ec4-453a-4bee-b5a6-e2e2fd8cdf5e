<template>
  <div class="app-container">
    <!-- 操作日志卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">操作日志</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="管理员">
            <el-select v-model="searchForm.aid" placeholder="请选择管理员" clearable>
              <el-option
                v-for="admin in adminList"
                :key="admin.id"
                :label="admin.name"
                :value="admin.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作内容">
            <el-input v-model="searchForm.operate" placeholder="请输入操作内容" clearable></el-input>
          </el-form-item>
          <el-form-item label="URL">
            <el-input v-model="searchForm.url" placeholder="请输入URL" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作日志列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="operateLogList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column label="类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getTypeTagType(scope.row.type)">
                {{ formatType(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="account" label="账号" width="120" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" width="120" align="center"></el-table-column>
          <el-table-column prop="ip" label="IP" width="140" align="center"></el-table-column>
          <el-table-column prop="url" label="URL" width="200">
            <template slot-scope="scope">
              <div class="url-content">{{ scope.row.url }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="operate" label="操作" width="150" align="center"></el-table-column>
          <el-table-column prop="remark" label="操作内容" min-width="200">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.remark" placement="top" effect="light">
                <div class="remark-content">
                  {{ scope.row.remark && scope.row.remark.length > 30 ? scope.row.remark.slice(0, 30) + '...' : scope.row.remark }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="操作时间" width="180" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { master_operate, master_search } from '@/api/sys'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  aid: '',
  operate: '',
  type: '',
  url: ''
})

// 定义表格数据
const operateLogList = ref([])
const adminList = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 类型选项
const typeOptions = [
  { label: '全部', value: '' },
  { label: '管理员', value: 1 },
  { label: '用户', value: 2 },
  { label: '渠道商', value: 3 }
]

// 获取管理员列表
const getAdminList = async () => {
  try {
    const res = await master_search()
    adminList.value = res.data.lists
  } catch (error) {
    console.error('获取管理员列表失败', error)
  }
}

// 获取操作日志列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await master_operate(params)
    operateLogList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取操作日志失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 格式化类型
const formatType = (row) => {
  switch (row.type) {
    case 1:
      return '管理员'
    case 2:
      return '用户'
    case 3:
      return '渠道商'
    default:
      return '未知'
  }
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  switch (type) {
    case 1:
      return 'primary'
    case 2:
      return 'success'
    case 3:
      return 'warning'
    default:
      return 'info'
  }
}

// 页面加载时获取数据
onMounted(() => {
  getAdminList()
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式

.url-content {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  max-width: 200px;
}

.remark-content {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  cursor: pointer;
}
</style>
