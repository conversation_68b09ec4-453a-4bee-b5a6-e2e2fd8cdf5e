import request from '@/utils/request'


export function master_list(data) {
  return request({
    url: '/master/list',
    method: 'post',
    data
  })
}

export function master_edit(data) {
  return request({
    url: '/master/edit',
    method: 'post',
    data
  })
}

export function master_add(data) {
  return request({
    url: '/master/add',
    method: 'post',
    data
  })
}

export function role_list(data) {
  return request({
    url: '/role/list',
    method: 'post',
    data
  })
}

export function role_add(data) {
  return request({
    url: '/role/add',
    method: 'post',
    data
  })
}

export function role_edit(data) {
  return request({
    url: '/role/edit',
    method: 'post',
    data
  })
}

export function role_menu(data) {
  return request({
    url: '/role/menu',
    method: 'post',
    data
  })
}

export function role_clear(data) {
  return request({
    url: '/role/clear',
    method: 'post',
    data
  })
}

export function menu_list(data) {
  return request({
    url: '/menu/list',
    method: 'post',
    data
  })
}

export function menu_edit(data) {
  return request({
    url: '/menu/edit',
    method: 'post',
    data
  })
}

export function menu_add(data) {
  return request({
    url: '/menu/add',
    method: 'post',
    data
  })
}

export function menu_del(data) {
  return request({
    url: '/menu/del',
    method: 'post',
    data
  })
}

export function menu_clear(data) {
  return request({
    url: '/menu/clear',
    method: 'post',
    data
  })
}

export function master_search(data) {
  return request({
    url: '/master/search',
    method: 'post',
    data
  })
}

export function master_operate(data) {
  return request({
    url: '/master/operate',
    method: 'post',
    data
  })
}
export function system_config(data) {
  return request({
    url: '/system/config',
    method: 'post',
    data
  })
}
export function system_edit(data) {
  return request({
    url: '/system/edit',
    method: 'post',
    data
  })
}

export function system_orders(data) {
  return request({
    url: '/system/orders',
    method: 'post',
    data
  })
}
export function system_order_edit(data) {
  return request({
    url: '/system/order_edit',
    method: 'post',
    data
  })
}

export function log_email(data) {
  return request({
    url: '/system/email',
    method: 'post',
    data
  })
}

export function version_list(data) {
  return request({
    url: '/version/list',
    method: 'post',
    data
  })
}

export function version_edit(data) {
  return request({
    url: '/version/edit',
    method: 'post',
    data
  })
}


export function home_country(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data
  })
}
