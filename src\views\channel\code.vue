<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { channel_code_list, channel_code_edit, channel_slist } from '@/api/channel'
// 定义搜索表单数据
const searchForm = reactive({
  channel_id: '',
  status: '',
  invite_code: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const tableData = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 渠道选项
const channelOptions = ref([])

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '2' }
]

// 邀请码表单对话框
const codeDialogVisible = ref(false)
const codeDialogTitle = ref('')
const isEdit = ref(false) // 是否是编辑模式
const codeForm = reactive({
  code_id: '0',
  channel_id: '',
  invite_code: '',
  remark: '',
  status: '1'
})

// 表单验证规则
const codeFormRules = {
  channel_id: [{ required: true, message: '请选择渠道', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 表单ref
const codeFormRef = ref(null)

// 获取渠道列表
const getChannelList = async () => {
  try {
    const res = await channel_slist({})
    if (res.code === 200) {
      channelOptions.value = res.data.map(item => ({
        label: item.account,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取渠道列表失败', error)
  }
}

// 获取邀请码列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await channel_code_list(params)
    tableData.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取邀请码列表失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 格式化状态
const formatStatus = (row) => {
  return row.status === 1 ? '启用' : '禁用'
}

// 格式化转化率
const formatConversionRate = (clickNum, regNum) => {
  // 如果点击数为0或者为空，返回 0.00%
  if (!clickNum || clickNum === 0) {
    return '0.00%'
  }

  // 计算转化率：注册数 / 点击数 * 100
  const rate = (regNum || 0) / clickNum * 100

  // 保留两位小数
  return rate.toFixed(2) + '%'
}

// 获取转化率颜色
const getConversionRateColor = (clickNum, regNum) => {
  if (!clickNum || clickNum === 0) {
    return '#909399' // 灰色 - 无数据
  }

  const rate = (regNum || 0) / clickNum * 100

  if (rate >= 10) {
    return '#67C23A' // 绿色 - 高转化率 (>=10%)
  } else if (rate >= 5) {
    return '#E6A23C' // 橙色 - 中等转化率 (5%-10%)
  } else if (rate >= 1) {
    return '#409EFF' // 蓝色 - 低转化率 (1%-5%)
  } else {
    return '#F56C6C' // 红色 - 极低转化率 (<1%)
  }
}

// 打开添加邀请码对话框
const handleAddCode = () => {
  isEdit.value = false
  codeDialogTitle.value = '新增渠道邀请码'
  // 重置表单
  Object.keys(codeForm).forEach(key => {
    if (key === 'code_id') {
      codeForm[key] = '0'
    } else if (key === 'status') {
      codeForm[key] = '1'
    } else {
      codeForm[key] = ''
    }
  })
  codeDialogVisible.value = true
}

// 打开编辑邀请码对话框
const handleEditCode = (row) => {
  isEdit.value = true
  codeDialogTitle.value = '编辑渠道邀请码'
  // 填充表单数据
  codeForm.code_id = row.id
  codeForm.channel_id = row.relation_id
  codeForm.invite_code = row.invite_code
  codeForm.remark = row.remark || ''
  codeForm.status = String(row.status)

  codeDialogVisible.value = true
}

// 提交邀请码表单
const submitCodeForm = async () => {
  if (!codeFormRef.value) return

  codeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await channel_code_edit(codeForm)
        if (res.code === 200) {
          codeDialogVisible.value = false
          getList() // 刷新列表
        }
      } catch (error) {
        console.error('提交邀请码数据失败', error)
      }
    } else {
      return false
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getChannelList()
  getList()
})
</script>

<template>
  <div class="app-container">
    <!-- 渠道邀请码管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">渠道邀请码管理</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddCode">添加邀请码</el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="渠道">
            <el-select v-model="searchForm.channel_id" placeholder="请选择渠道" clearable>
              <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="邀请码">
            <el-input v-model="searchForm.invite_code" placeholder="请输入邀请码" clearable></el-input>
          </el-form-item>
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="searchForm.ct_start"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="searchForm.ct_end"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 邀请码列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="tableData"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="50" align="center"></el-table-column>
          <el-table-column prop="relation_acc" label="渠道账号" align="center" min-width="120"></el-table-column>
          <el-table-column prop="invite_code" label="邀请码" align="center" min-width="120"></el-table-column>
          <el-table-column label="状态" width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ formatStatus(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="click_num" label="点击" align="center"></el-table-column>
          <el-table-column prop="reg_num" label="注册" align="center"></el-table-column>
          <el-table-column label="转化率" align="center" width="100">
            <template slot-scope="scope">
              <span :style="{ color: getConversionRateColor(scope.row.click_num, scope.row.reg_num), fontWeight: 'bold' }">
                {{ formatConversionRate(scope.row.click_num, scope.row.reg_num) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
          <el-table-column label="创建/更新时间" width="160" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.create_time }}</div>
              {{ scope.row.update_time }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditCode(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 邀请码编辑对话框 -->
    <el-dialog
      :title="codeDialogTitle"
      :visible.sync="codeDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="codeFormRef"
        :model="codeForm"
        :rules="codeFormRules"
        label-width="100px"
        status-icon
      >
        <el-form-item label="渠道" prop="channel_id" >
          <el-select v-model="codeForm.channel_id" placeholder="请选择渠道" style="width: 100%" :disabled="isEdit">
            <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="isEdit" label="邀请码">
          <el-input v-model="codeForm.invite_code" disabled placeholder="邀请码不可修改"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="codeForm.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="codeForm.remark"
            type="textarea"
            placeholder="请输入备注内容"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="codeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCodeForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
