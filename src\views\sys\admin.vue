<template>
  <div class="app-container">
    <!-- 管理员管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">管理员管理</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddAdmin">新增管理员</el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="账户名">
            <el-input v-model="searchForm.account" placeholder="请输入账户名" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.disable" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.mobile" placeholder="请输入手机号" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 管理员列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="adminList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="account" label="账号" width="120" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" width="120" align="center"></el-table-column>
          <el-table-column prop="mobile" label="手机号" width="120" align="center"></el-table-column>
          <el-table-column prop="role_name" label="所属角色组" width="120" align="center"></el-table-column>
          <el-table-column label="状态" width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.disable === 1 ? 'success' : 'danger'">
                {{ scope.row.disable === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="login_num" label="登录次数" width="100" align="center"></el-table-column>
          <el-table-column prop="login_time" label="最后登录时间" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditAdmin(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 新增管理员对话框 -->
    <el-dialog
      title="新增管理员"
      :visible.sync="addDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="角色" prop="rid">
          <el-select v-model="addForm.rid" placeholder="请选择角色">
            <el-option
              v-for="role in roleList"
              :key="role.role_id"
              :label="role.role_name"
              :value="role.role_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账户名" prop="account">
          <el-input v-model="addForm.account" placeholder="请输入账户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="addForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="管理员姓名" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入管理员姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="addForm.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑管理员对话框 -->
    <el-dialog
      title="编辑管理员"
      :visible.sync="editDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="账号" prop="account">
          <el-input v-model="editForm.account" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="editForm.password" type="password" placeholder="不修改请留空"></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="editForm.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="所属角色组" prop="rid">
          <el-select v-model="editForm.rid" placeholder="请选择角色">
            <el-option
              v-for="role in roleList"
              :key="role.role_id"
              :label="role.role_name"
              :value="role.role_id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="disable">
          <el-radio-group v-model="editForm.disable">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { master_list, master_edit, master_add, role_list } from '@/api/sys'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  account: '',
  disable: '',
  mobile: ''
})

// 定义表格数据
const adminList = ref([])
const roleList = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '2' }
]

// 新增管理员对话框
const addDialogVisible = ref(false)
const addForm = reactive({
  rid: '',
  account: '',
  password: '',
  name: '',
  mobile: ''
})

// 新增表单验证规则
const addFormRules = {
  rid: [{ required: true, message: '请选择角色', trigger: 'change' }],
  account: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
  mobile: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }]
}

// 新增表单ref
const addFormRef = ref(null)

// 编辑管理员对话框
const editDialogVisible = ref(false)
const editForm = reactive({
  rid: '',
  account: '',
  password: '',
  name: '',
  mobile: '',
  disable: '',
  aid: ''
})

// 编辑表单验证规则
const editFormRules = {
  account: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
  name: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
  mobile: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
  rid: [{ required: true, message: '请选择角色', trigger: 'change' }],
  disable: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 编辑表单ref
const editFormRef = ref(null)

// 获取管理员列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await master_list(params)
    adminList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取管理员列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取角色列表数据
const getRoleList = async () => {
  try {
    const res = await role_list()
    roleList.value = res.data.lists
  } catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 打开添加管理员对话框
const handleAddAdmin = () => {
  // 重置表单
  Object.keys(addForm).forEach(key => {
    addForm[key] = ''
  })
  addDialogVisible.value = true
}

// 打开编辑管理员对话框
const handleEditAdmin = (row) => {
  // 填充表单数据
  editForm.rid = row.role_id || ''
  editForm.account = row.account || ''
  editForm.password = ''
  editForm.name = row.name || ''
  editForm.mobile = row.mobile || ''
  editForm.disable = row.disable || ''
  editForm.aid = row.id || ''

  editDialogVisible.value = true
}

// 提交新增表单
const submitAddForm = async () => {
  if (!addFormRef.value) return

  addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await master_add(addForm)
        addDialogVisible.value = false
        getList() // 刷新列表
      } catch (error) {
        console.error('添加管理员失败:', error)
      }
    } else {
      return false
    }
  })
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await master_edit(editForm)
        editDialogVisible.value = false
        getList() // 刷新列表
      } catch (error) {
        console.error('编辑管理员失败:', error)
      }
    } else {
      return false
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getList()
  getRoleList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
