<template>
  <div class="app-container">
    <!-- 登录日志卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">登录日志</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户账号">
            <el-input v-model="searchForm.account" placeholder="请输入用户账号" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.type" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="登录设备">
            <el-select v-model="searchForm.os" placeholder="请选择设备" clearable>
              <el-option v-for="item in osOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 登录日志列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="loginLogList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
           height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="account" label="账号" align="center">
            <template slot-scope="scope">
              {{ scope.row.account }}_{{ scope.row.os }}
            </template>
          </el-table-column>
          <el-table-column label="IP" align="center">
            <template slot-scope="scope">
              <div>IP：{{ scope.row.login_ip }}</div>
              MAC：{{ scope.row.mac || '--'}}
            </template>
          </el-table-column>
          <el-table-column label="流量" align="center">
            <template slot-scope="scope">
              <div>上行：{{ scope.row.total_up }}</div>
              下行：{{ scope.row.total_down }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template slot-scope="scope">
              <div>
                <el-tag v-if="scope.row.type === 1" type="success">在线:{{ scope.row.line_time }}</el-tag>
                <el-tag v-else-if="scope.row.type === 2" type="primary">离线:{{ scope.row.line_time }}</el-tag>
                <el-tag v-else type="primary">被挤掉:{{ scope.row.line_time }}</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="last_time" label="最后心跳时间" align="center"></el-table-column>
          <el-table-column prop="out_time" label="离线时间" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { member_login_log } from '@/api/member'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  account: '',
  os: '',
  type: ''
})

// 定义表格数据
const loginLogList = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '在线', value: '1' },
  { label: '离线', value: '2' },
  { label: '被挤掉离线', value: '3' }
]

// 设备选项
const osOptions = [
  { label: '全部', value: '' },
  { label: 'windows', value: 'windows' },
  { label: 'android', value: 'android' },
  { label: 'ios', value: 'ios' }
]

// 获取登录日志列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await member_login_log(params)
    loginLogList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取登录日志失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
