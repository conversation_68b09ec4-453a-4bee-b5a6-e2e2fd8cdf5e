<template>
  <el-dialog :visible.sync="dialog" title="绑定谷歌验证器" width="500px" :close-on-click-modal="false" :before-close="close">
    <div class="qr" v-loading="loading">
        <img :src="qrCodeUrl" alt="" width="100%">
    </div>
  </el-dialog>
</template>

<script>
import {bindGoogle} from '@/api/user'
export default {
    data(){
        return{
            dialog: false,
            qrCodeUrl: '',
            loading: false,
        }
    },
    methods:{
        open(req){
            this.dialog = true
            this.bindGoogle(req)
        },
        close(){
            this.dialog = false
            this.qrCodeUrl = ''
        },
        async bindGoogle(req){
            try{
                this.loading = true
                let {data} = await bindGoogle(req)
                this.qrCodeUrl = data.qrCodeUrl
                this.loading = false
            }catch(err){
                this.loading = false
                this.close()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.qr{
    margin: 0 auto;
    width: 350px;
    min-height: 300px;
    background: transparent;
}
</style>