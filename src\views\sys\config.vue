<template>
  <div class="app-container">
    <el-card class="page-card">
      <el-tabs v-model="activeTab">

        <el-tab-pane label="系统配置" name="system">
          <el-form :model="form" :rules="rules" ref="systemForm" label-width="140px">
            <el-form-item label="试用赠送" prop="data.open_trial">
              <el-radio-group v-model="form.data.open_trial">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="赠送分钟" prop="data.trial_minute">
              <el-input type="number" v-model="form.data.trial_minute" placeholder="请输入赠送分钟" style="width: 30%;" >
                <template #suffix>分钟</template>
              </el-input>
            </el-form-item>
            <el-form-item label="每日人数" prop="data.trial_num">
              <el-input type="number" v-model="form.data.trial_num" placeholder="请输入赠送每日人数" style="width: 30%;" >
                <template #suffix>人</template>
              </el-input>
            </el-form-item>
            <el-divider></el-divider>
            <el-form-item label="用户名注册" prop="data.open_account">
              <el-radio-group v-model="form.data.open_account">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="邮箱注册验证码" prop="data.open_email">
              <el-radio-group v-model="form.data.open_email">
                <el-radio :label="1">必须</el-radio>
                <el-radio :label="2">不必须</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-divider></el-divider>
            <el-form-item label="日志转发" prop="data.open_forward">
              <el-radio-group v-model="form.data.open_forward">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
                <el-radio :label="3">彻底关闭（数据库也不写入）</el-radio>
              </el-radio-group>
              <div class="form-tip">开启后，会转发到指定地址，关闭后只在数据库中写入，彻底关闭后不再转发和写入数据库</div>
            </el-form-item>
            <el-form-item label="转发地址" prop="data.forward_url">
              <el-input v-model="form.data.forward_url" placeholder="请输入转发地址" style="width: 40%;"></el-input>
              <div class="form-tip">转发参数 json ：[{"username":"test","password":"test","country_code":"PA","server_num":1,"host":"127.0.0.1","remote_ip":"127.0.0.1","create_time":*********},{"username":"test","password":"test","country_code":"PA","server_num":1,"host":"127.0.0.1","remote_ip":"127.0.0.1","create_time":*********}]</div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitSystemForm">保存配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- SMTP配置 -->
        <el-tab-pane label="邮件SMTP配置" name="smtp">
          <el-form :model="form" :rules="rules" ref="smtpForm" label-width="140px">
            <el-form-item label="账户" prop="data.account">
              <el-input v-model="form.data.account" placeholder="请输入SMTP账户" style="width: 40%;"></el-input>
            </el-form-item>
            <el-form-item label="服务器地址" prop="data.server">
              <el-input v-model="form.data.server" placeholder="请输入SMTP服务器地址" style="width: 40%;"></el-input>
            </el-form-item>
            <el-form-item label="端口" prop="data.port">
              <el-input type="number" v-model="form.data.port" placeholder="请输入SMTP端口" style="width: 40%;"></el-input>
            </el-form-item>
            <el-form-item label="发送者邮箱" prop="data.from">
              <el-input v-model="form.data.from" placeholder="请输入发送者邮箱" style="width: 40%;"></el-input>
            </el-form-item>
            <el-form-item label="访问凭证" prop="data.password">
              <el-input v-model="form.data.password" placeholder="请输入访问凭证" style="width: 40%;"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitSmtpForm">保存配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="STRIPE配置" name="stripe">
          <el-form :model="form" :rules="rules" ref="stripeForm" label-width="140px">
            <el-form-item label="API私钥" prop="data.priKey">
              <el-input v-model="form.data.priKey" placeholder="请输入API私钥"></el-input>
            </el-form-item>
            <el-form-item label="API公钥" prop="data.pubKey">
              <el-input v-model="form.data.pubKey" placeholder="请输入API公钥"></el-input>
            </el-form-item>
            <el-form-item label="webhook签名" prop="data.whsec">
              <el-input v-model="form.data.whsec" placeholder="请输入webhook签名"></el-input>
              <div class="form-tip">stripe 的 webhook 回调地址：https://your-domain.com/notify/pay 例如：https://testapi.softapi.cn/notify/pay</div>
            </el-form-item>
            <el-form-item label="支付成功回调" prop="data.success_url">
              <el-input v-model="form.data.success_url" placeholder="请输入支付成功回调"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitStripeForm">保存配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import {system_config, system_edit} from '@/api/sys'

export default {
  name: 'SystemConfig',
  data() {
    return {
      activeTab: 'system',
      form: {
        data: {
          // TRIAL配置
          open_trial: '',
          trial_minute: '',
          trial_num: '',
          open_account: '',
          open_email: '',
          open_forward: '',
          forward_url: '',
          open_invite: '',
          invite_percent: '',
          // SMTP配置
          account: '',
          server: '',
          port: '',
          from: '',
          password: '',
          // STRIPE 配置
          priKey: '',
          pubKey: '',
          whsec: '',
          success_url: '',
        }
      },
      rules: {
        'data.invite_percent': [
          {required: true, message: '请输入渠道商返现比例', trigger: 'blur'}
        ],
        'data.account': [
          {required: true, message: '请输入SMTP账户', trigger: 'blur'}
        ],
        'data.server': [
          {required: true, message: '请输入SMTP服务器地址', trigger: 'blur'}
        ],
        'data.port': [
          {required: true, message: '请输入SMTP端口', trigger: 'blur'}
        ],
        'data.from': [
          {required: true, message: '请输入发送者邮箱', trigger: 'blur'}
        ],
        'data.password': [
          {required: true, message: '请输入访问凭证', trigger: 'blur'}
        ],
        'data.priKey': [
          {required: true, message: '请输入API私钥', trigger: 'blur'}
        ],
        'data.pubKey': [
          {required: true, message: '请输入API公钥', trigger: 'blur'}
        ],
        'data.whsec': [
          {required: true, message: '请输入webhook签名', trigger: 'blur'}
        ],
        'data.success_url': [
          {required: true, message: '请输入支付成功回调', trigger: 'blur'}
        ],
        'data.appid': [
          {required: true, message: '请输入APPLEPAY的Appid', trigger: 'blur'}
        ],
        'data.AppSecret': [
          {required: true, message: '请输入APPLEPAY的AppSecret', trigger: 'blur'}
        ]
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    // 获取配置
    async getConfig() {
      try {
        const res = await system_config()
        // 处理返回的数据
        const configData = res.data.group_list

        // APPLE配置
        const systemConfig = configData.SYSTEM
        systemConfig.forEach(item => {
          if (item.is_switch === 1) {
            this.form.data[item.field] = Number(item.value)
          } else {
            this.form.data[item.field] = item.value
          }
        })
        console.log(systemConfig)

        // SMTP配置
        const smtpConfig = configData.SMTP
        smtpConfig.forEach(item => {
          if (item.is_switch === 1) {
            this.form.data[item.field] = Number(item.value)
          } else {
            this.form.data[item.field] = item.value
          }
        })

        // STRIPE配置
        const stripeConfig = configData.STRIPE
        stripeConfig.forEach(item => {
          if (item.is_switch === 1) {
            this.form.data[item.field] = Number(item.value)
          } else {
            this.form.data[item.field] = item.value
          }
        })

      } catch (error) {
        console.error(error)
      }
    },
    // 提交SMTP配置
    submitSmtpForm() {
      this.$refs.smtpForm.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = {
              data: JSON.stringify({
                account: this.form.data.account,
                server: this.form.data.server,
                port: this.form.data.port,
                from: this.form.data.from,
                password: this.form.data.password
              })
            }
            await system_edit(submitData)
            this.$message.success('保存成功')
            await this.getConfig()
          } catch (error) {
            console.error(error)
          }
        }
      })
    },
    // 提交STRIPE配置
    submitStripeForm() {
      this.$refs.stripeForm.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = {
              data: JSON.stringify({
                priKey: this.form.data.priKey,
                pubKey: this.form.data.pubKey,
                whsec: this.form.data.whsec,
                success_url: this.form.data.success_url
              })
            }
            await system_edit(submitData)
            this.$message.success('保存成功')
            await this.getConfig()
          } catch (error) {
            console.error(error)
          }
        }
      })
    },
    // 提交system配置
    submitSystemForm() {
      this.$refs.systemForm.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = {
              data: JSON.stringify({
                open_account: this.form.data.open_account,
                open_email: this.form.data.open_email,
                open_forward: this.form.data.open_forward,
                forward_url: this.form.data.forward_url,
                open_trial: this.form.data.open_trial,
                trial_minute: this.form.data.trial_minute,
                trial_num: this.form.data.trial_num
              })
            }
            await system_edit(submitData)
            this.$message.success('保存成功')
            await this.getConfig()
          } catch (error) {
            console.error(error)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.el-form-item {
  margin-bottom: 22px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-form-item__label {
  padding-right: 20px;
}

::v-deep {
  .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      overflow: auto;
      flex: 1;
    }
  }
}
</style>
