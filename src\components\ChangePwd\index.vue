<template>
  <el-dialog :visible.sync="dialog" :append-to-body="true" :before-close="close" title="修改密码" width="400px" :close-on-click-modal="false">
    <el-form ref="form" :rules="rules" :model="form">
        <el-form-item prop="old_pwd" label="旧密码">
            <el-input v-model="form.old_pwd" placeholder="输入旧密码"></el-input>
        </el-form-item>
        <el-form-item prop="new_pwd" label="新密码">
            <el-input v-model="form.new_pwd" placeholder="输入新密码"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" :loading="loading" @click="master_pass" style="width:100%">确定</el-button>
        </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import {master_pass} from '@/api/user'
export default {
    data(){
        return{
            dialog: false,
            form:{
                old_pwd: '',
                new_pwd: '',
            },
            rules: {
                old_pwd: [
                    {required: true, message: '请输入旧密码',trigger: 'blur'}
                ],
                new_pwd: [
                    {required: true, message: '请输入新密码',trigger: 'blur'}
                ]
            },
            loading: false,
        }
    },
    methods:{
        open(){
            this.dialog = true
        },
        close(){
            this.form = {
                old_pwd: '',
                new_pwd: '',
            }
            this.dialog = false
        },
        master_pass(){
            this.$refs.form.validate(valid => {
                if(valid){
                    this.loading = true
                    let req = {
                        pass: this.form.old_pwd,
                        repass: this.form.new_pwd,
                    }
                    master_pass(req).then(res => {
                        this.loading = false
                        this.$message.success('修改成功')
                        this.close()
                    }).catch(err => {
                        this.loading = false
                    })
                }
            })
        }
    }
}
</script>

<style>

</style>