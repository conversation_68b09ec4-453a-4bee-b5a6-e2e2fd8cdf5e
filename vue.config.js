"use strict";
const path = require("path");

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  publicPath: "/",
  outputDir: "dist",
  assetsDir: "static",
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    compress: false,
    port: 9555,
    proxy: {
      "/admin": {
        ws: false,
        // target: "https://testapi.softapi.cn/admin",
        target: "http://192.168.31.13:9522/admin",
        changeOrigin: true,
        pathRewrite: {
          "^/admin": "",
        },
      },
    }, client: {
      overlay: false
    }
  }, css: {
    loaderOptions: {
      sass: {    // 这里开始是新增的配置
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  }, configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: 'cloudServer', resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
  }
};
