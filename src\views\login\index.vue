<template>
  <div class="login-wrap">
    <div class="main-wrap">
        <Login :formType.sync="formType" v-if="formType === 'login'" />
    </div>
  </div>
</template>

<script>
import Login from './components/Login.vue'

export default {
    components:{
        Login,
    },
    data(){
        return{
            formType: 'login'
        }
    },
}
</script>

<style lang="scss" scoped>
.login-wrap{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100vh;
    background-image: url('@/assets/imgs/login_bg.png');
    background-size: cover;
    .main-wrap{
        flex: 1 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>