<script setup>
import { ref, reactive, onMounted } from 'vue'
import { channel_login_log, channel_slist } from '@/api/channel'

// 定义搜索表单数据
const searchForm = reactive({
  channel_id: '',
  type: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const tableData = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 渠道选项
const channelOptions = ref([])

// 登录类型选项
const typeOptions = [
  { label: '全部', value: '' },
  { label: '登录', value: '1' },
  { label: '退出', value: '2' },
  { label: '被挤掉退出', value: '3' }
]

// 获取渠道列表
const getChannelList = async () => {
  try {
    const res = await channel_slist({})
    if (res.code === 200) {
      channelOptions.value = res.data.map(item => ({
        label: item.account,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取渠道列表失败', error)
  }
}

// 获取登录日志列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await channel_login_log(params)
    tableData.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取登录日志列表失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 格式化登录类型
const formatType = (row) => {
  switch (row.type) {
    case 1:
      return '登录'
    case 2:
      return '退出'
    case 3:
      return '被挤掉退出'
    default:
      return '未知'
  }
}

// 页面加载时获取数据
onMounted(() => {
  getChannelList()
  getList()
})
</script>

<template>
  <div class="app-container">
    <!-- 渠道登录日志卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">渠道登录日志</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="渠道">
            <el-select v-model="searchForm.channel_id" placeholder="请选择渠道" clearable>
              <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="登录日期">
            <el-date-picker
              v-model="searchForm.ct_start"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="searchForm.ct_end"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 登录日志列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="tableData"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="channel_id" label="渠道ID" width="80" align="center"></el-table-column>
          <el-table-column prop="account" label="渠道账号" align="center" min-width="120"></el-table-column>
          <el-table-column label="登录类型" width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 1 ? 'success' : (scope.row.type === 2 ? 'info' : 'danger')">
                {{ formatType(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="login_ip" label="登录IP" align="center" width="140"></el-table-column>
          <el-table-column prop="last_time" label="登录时间" align="center" width="160"></el-table-column>
          <el-table-column prop="token" label="Token" align="center" min-width="280" show-overflow-tooltip></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
