<template>
  <el-dialog :visible.sync="dialog" :append-to-body="true" :before-close="close" :close-on-click-modal="false" title="添加中转服务器" width="500px">
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-form-item prop="region_id" label="地区">
        <el-select v-model="form.region_id" placeholder="请选择地区">
          <el-option
            v-for="item in regionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="GUID" prop="guid">
          <el-input v-model="form.guid" placeholder="请输入中转服务器的GUID"></el-input>
        </el-form-item>
      <el-form-item prop="ip" label="IP">
        <el-input v-model="form.ip" placeholder="请输入IP"></el-input>
      </el-form-item>
      <el-form-item prop="port" label="端口号">
        <el-input v-model="form.port" placeholder="请输入端口号"></el-input>
      </el-form-item>
      <el-form-item prop="password" label="密码">
        <el-input v-model="form.password" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item prop="method" label="加密方式">
        <el-select v-model="form.method" placeholder="请选择加密方式">
          <el-option
            v-for="item in proxyMethods"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="带宽(M)" prop="broadband">
          <el-input type="number" v-model="form.broadband" placeholder="请输带宽(M)"></el-input>
        </el-form-item>
        <el-form-item label="内存(M)" prop="memory">
          <el-input type="number"  v-model="form.memory" placeholder="请输内存(M)"></el-input>
        </el-form-item>
      <el-form-item prop="status" label="状态">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="2">禁用</el-radio>
          <el-radio :label="3">离线</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="submitForm" style="width:100%">确定</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { region_list, region_changes_edit } from '@/api/server'

export default {
  data() {
    return {
      dialog: false,
      form: {
        region_id: '',
        guid:'',
        ip: '',
        port: '',
        password: '',
        method: '',
        broadband: 0,
        memory: 0,
        status: 1
      },
      rules: {
        region_id: [
          { required: true, message: '请选择地区', trigger: 'change' }
        ],
        guid: [
          { required: true, message: '请输入GUID', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '请输入IP', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请选择加密方式', trigger: 'change' }
        ],
        broadband: [
          { required: true, message: '请输入带宽(M)', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('带宽必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        memory: [
          { required: true, message: '请输入内存(M)', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('内存必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      regionOptions: [],
      proxyMethods: [
        { value: 'aes-128-gcm', label: 'aes-128-gcm' },
        { value: 'aes-192-gcm', label: 'aes-192-gcm' },
        { value: 'aes-256-gcm', label: 'aes-256-gcm' },
        { value: 'aes-128-cfb', label: 'aes-128-cfb' },
        { value: 'aes-192-cfb', label: 'aes-192-cfb' },
        { value: 'aes-256-cfb', label: 'aes-256-cfb' },
        { value: 'rc4-md5', label: 'rc4-md5' },
        { value: 'chacha20', label: 'chacha20' }
      ],
      loading: false
    }
  },
  methods: {
    open() {
      this.dialog = true
      this.fetchRegions()
    },
    close() {
      this.resetForm()
      this.dialog = false
    },
    resetForm() {
      this.form = {
        region_id: '',
        guid: '',
        ip: '',
        port: '',
        password: '',
        method: '',
        broadband: 0,
        memory: 0,
        status: 1
      }
    },
    async fetchRegions() {
      try {
        const res = await region_list({})
        if (res.code === 200 && res.data && res.data.lists) {
          this.regionOptions = res.data.lists.map(item => ({
            label: `${item.cn_name}-${item.regin_name}`,
            value: item.id
          }))
        }
      } catch (error) {
        console.error('获取地区列表失败:', error)
      }
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          region_changes_edit(this.form).then(res => {
            this.loading = false
            if (res.code === 200) {
              this.$message.success('添加成功')
              this.$emit('success')
              this.close()
            } else {
              this.$message.error(res.msg || '添加失败')
            }
          }).catch(err => {
            console.error('添加中转服务器失败:', err)
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
