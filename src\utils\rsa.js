import {JSEncrypt} from './jsencrypt'

let pubKey = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCXUNRVjt9jwnmrIWBXykLr2r0J
I47PxSDCjw1SE7gPAR74fXctki3a0x9fFK0P5X9ujmp/zAveG124ddLsY4mM/d+O
5hietIbyaAVTYrSN4chDWVyei++ggQtxs3LgYHHQYvu02TBEuWcNjNyG8/NzPw9F
vVj33RXusklbXWLn8QIDAQAB
-----E<PERSON> PUBLIC KEY-----`


// 解密
export function rsaDecrypt(params) {
    var verify = new JSEncrypt();
    verify.setPublicKey(pubKey);
    // var verified = verify.decrypt(params);
    var verified = verify.decryptLong(params);
    return verified
}

// 加密
export function rsaEncrypt(params) {
    var sign = new JSEncrypt();
    sign.setPrivateKey(pubKey);
    var signature = sign.encryptLong(params);
    return signature
}