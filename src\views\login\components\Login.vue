<template>
  <div class="form-wrap">
    <h5 class="head">
      <img src="@/assets/imgs/logo.png" alt="">
      <div>登录</div>
    </h5>
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item prop="account">
        <el-input v-model="form.account" placeholder="账户"></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="form.password" :type="passType" placeholder="密码">
          <i slot="suffix" class="el-input__icon iconfont pointer" :class="passType === 'password' ? 'icon-no_eye' : 'icon-eye'" @click="changePassType"></i>
        </el-input>
      </el-form-item>
      <el-form-item prop="auth_code">
        <div class="flex no-wrap">
          <el-input v-model="form.auth_code" placeholder="谷歌验证码"></el-input>
          <el-button class="q-ml-md" type="primary" @click="bindGoogle">绑定谷歌验证器</el-button>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" style="width:100%" @click="login" :loading="loading">登录</el-button>
      </el-form-item>
    </el-form>
    <BindGoogle ref="bindGoogle" />
  </div>
</template>

<script>
import BindGoogle from './BindGoogle.vue'
import { deepClone } from '@/utils/index'
export default {
  props: ['formType'],
  components: {
    BindGoogle
  },
  data() {

    return {
      form: {
        account: '',
        password: '',
        auth_code: ''
      },
      rules: {
        account: [
          { required: true, message: '请输入账户', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
        ],
        auth_code: [
          { required: true, message: '请输入谷歌验证码', trigger: 'blur' },
        ]
      },
      passType: 'password',
      loading: false,
    }
  },
  methods: {
    changePassType() {
      if (this.passType === 'password') {
        this.passType = 'text'
      } else {
        this.passType = 'password'
      }
    },
    login() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          let reqData = deepClone(this.form)
          reqData.code = reqData.auth_code
          delete reqData.auth_code
          // reqData.password = window.btoa(reqData.password)
          this.$store.dispatch('user/login', reqData).then(res => {
            this.loading = false
            // 获取重定向参数
            const redirect = this.$route.query.redirect
            if (redirect) {
              this.$router.push(redirect)
            } else {
              // 如果没有重定向参数，跳转到第一个菜单页面
              const userInfo = res.data
              if (userInfo && userInfo.menuList && userInfo.menuList.length > 0) {
                const firstMenu = userInfo.menuList[0]
                if (firstMenu.type === 1 && firstMenu.menu && firstMenu.menu.length > 0) {
                  this.$router.push(firstMenu.menu[0].show_action)
                } else {
                  this.$router.push(firstMenu.show_action)
                }
              } else {
                // 如果没有菜单权限，刷新页面让路由守卫处理
                window.location.reload()
              }
            }
          }).catch(err => {
            this.loading = false
          })
        }
      })
    },
    bindGoogle() {
      let validateFieldList = []
      this.$refs.form.validateField(['account', 'password'], valid => {
        if (!valid) {
          validateFieldList.push(valid);
          if (
            validateFieldList.length == 2 &&
            validateFieldList.every((item) => item === "")
          ) {
            let { account, password } = this.form
            let req = {
              account,
              password
            }
            this.$refs.bindGoogle.open(req)
          }

          return;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.head {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  img {
    width: 70px;
    margin-bottom: 20px;
  }
}
.form-wrap {
  box-shadow: #919eab33 0 0 2px, #919eab1f 0 12px 24px -4px !important;
  padding: 20px;
  border-radius: 16px;
  background: #fff;
}
</style>
