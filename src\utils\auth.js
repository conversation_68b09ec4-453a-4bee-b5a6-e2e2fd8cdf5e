const TokenKey = "Token_admin";
const userKey = "User_admin";

export function getToken() {
  return localStorage.getItem(TokenKey);
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token);
}

export function removeToken() {
  return localStorage.removeItem(TokenKey);
}

export function getUserInfo() {
  let info = localStorage.getItem(userKey);
  if (!!info) {
    return JSON.parse(info);
  } else {
    return null;
  }
}

export function setUserInfo(data) {
  return localStorage.setItem(userKey, JSON.stringify(data));
}

export function removeUserInfo() {
  return localStorage.removeItem(userKey);
}
