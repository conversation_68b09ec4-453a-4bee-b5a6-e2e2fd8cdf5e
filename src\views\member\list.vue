<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { member_list, member_edit, member_del } from '@/api/member'
import { channel_slist } from '@/api/channel'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  account: '',
  channel_id: '',
  status: '',
  speed_limit: '',
  invite_type: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const tableData = ref([])
const loading = ref(false)

// 密码可见性状态 - 初始化为空对象
const passwordVisible = ref({})

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '2' }
]

// 限速选项
const speedLimitOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '2' }
]

// 渠道选项
const channelOptions = ref([])

// 注册类型选项
const inviteTypeOptions = [
  { label: '全部', value: '' },
  { label: '自注册', value: '1' },
  { label: '用户邀请', value: '2' },
  { label: '渠道邀请', value: '3' }
]

// 用户表单对话框
const userDialogVisible = ref(false)
const userDialogTitle = ref('')
const userForm = reactive({
  member_id: '0',
  account: '',
  password: '',
  status: '1',
  speed_limit: '2',
  up_speed: '',
  down_speed: '',
  client_num: '',
  remark: '',
  expire_user: '',
  region_level: '1',
  level_lock: '2'
})

// 表单验证规则
const userFormRules = {
  account: [{ required: true, message: '请输入登录账户', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  speed_limit: [{ required: true, message: '请选择限速状态', trigger: 'change' }],
  expire_user: [{ required: true, message: '请选择过期时间', trigger: 'change' }],
  client_num: [{ required: true, message: '请输入最大连接数', trigger: 'blur' }],
  region_level: [
    { required: true, message: '请输入地区等级', trigger: 'blur' }
  ],
  level_lock: [{ required: true, message: '请选择锁定状态', trigger: 'change' }]
}

// 用户表单ref
const userFormRef = ref(null)

// 获取渠道列表
const getChannelList = async () => {
  try {
    const res = await channel_slist({})
    if (res.code === 200) {
      channelOptions.value = [
        { label: '全部', value: '' },
        ...res.data.map(item => ({
          label: item.account,
          value: item.id
        }))
      ]
    }
  } catch (error) {
    console.error('获取渠道列表失败', error)
  }
}

// 获取用户列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await member_list(params)
    tableData.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page

    // 重置密码可见性 - 确保是一个对象
    passwordVisible.value = {}
  } catch (error) {
    console.error('获取用户列表失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 格式化状态
const formatStatus = (row) => {
  return row.status === 1 ? '启用' : '禁用'
}

// 格式化限速状态
const formatSpeedLimit = (row) => {
  return row.speed_limit === 1 ? '启用' : '禁用'
}

// 格式化订阅字段
const formatSubscribe = (subscribe) => {
  if (!subscribe) return '-'
  // 如果以 "Surfelves" 开头，则去掉这个前缀
  if (subscribe.startsWith('Surfelves')) {
    return subscribe.replace('Surfelves', '').replace('Month', ''); // 去掉 "Surfelves" (9个字符)
  }
  return subscribe
}

// 格式化等级锁定状态
const formatLevelLock = (levelLock) => {
  return levelLock === 1 ? '锁定' : '未锁定'
}

// 格式化注册类型
const formatInviteType = (inviteType) => {
  switch (inviteType) {
    case 1:
    case '1':
      return '自注册'
    case 2:
    case '2':
      return '用户邀请'
    case 3:
    case '3':
      return '渠道邀请'
    default:
      return '未知'
  }
}

// 获取注册类型颜色
const getInviteTypeColor = (inviteType) => {
  switch (inviteType) {
    case 1:
    case '1':
      return '#909399' // 灰色 - 自注册
    case 2:
    case '2':
      return '#67C23A' // 绿色 - 用户邀请
    case 3:
    case '3':
      return '#409EFF' // 蓝色 - 渠道邀请
    default:
      return '#F56C6C' // 红色 - 未知
  }
}

// 获取邀请人数颜色
const getInviteNumColor = (inviteNum) => {
  const num = inviteNum || 0
  if (num >= 10) {
    return '#67C23A' // 绿色 - 邀请人数多 (>=10人)
  } else if (num >= 5) {
    return '#E6A23C' // 橙色 - 邀请人数中等 (5-9人)
  } else if (num >= 1) {
    return '#409EFF' // 蓝色 - 邀请人数少 (1-4人)
  } else {
    return '#909399' // 灰色 - 无邀请 (0人)
  }
}

// 检查密码是否显示
const isPasswordVisible = (id) => {
  return passwordVisible.value && passwordVisible.value[id] === true
}

// 切换密码显示状态
const togglePasswordVisibility = (id) => {
  console.log('切换密码显示状态，ID:', id)

  // 确保passwordVisible.value是一个对象
  if (!passwordVisible.value) {
    passwordVisible.value = {}
  }

  // 使用Vue的方式设置对象属性，确保响应式更新
  passwordVisible.value = {
    ...passwordVisible.value,
    [id]: !passwordVisible.value[id]
  }

  console.log('密码显示状态已切换，现在是否显示:', passwordVisible.value[id])
}

// 打开添加用户对话框
const handleAddUser = () => {
  userDialogTitle.value = '新增用户'
  // 重置表单
  Object.keys(userForm).forEach(key => {
    if (key === 'member_id') {
      userForm[key] = '0'
    } else if (key === 'status') {
      userForm[key] = '1'
    } else if (key === 'speed_limit') {
      userForm[key] = '2'
    } else if (key === 'region_level') {
      userForm[key] = 1
    } else if (key === 'level_lock') {
      userForm[key] = '2'
    } else {
      userForm[key] = ''
    }
  })
  userDialogVisible.value = true
}

// 打开编辑用户对话框
const handleEditUser = (row) => {
  userDialogTitle.value = '编辑用户'
  // 填充表单数据
  Object.keys(userForm).forEach(key => {
    if (key === 'member_id') {
      userForm[key] = row.id
    } else if (key === 'password') {
      userForm[key] = row.password || '' // 编辑时显示原密码
    } else if (key in row) {
      userForm[key] = row[key]
    }
  })

  // 直接设置所有字段值，确保数据正确填充
  userForm.member_id = row.id || 0
  userForm.account = row.account || ''
  userForm.status = String(row.status) || '1'
  userForm.speed_limit = String(row.speed_limit) || '2'
  userForm.up_speed = row.up_speed || ''
  userForm.down_speed = row.down_speed || ''
  userForm.remark = row.remark || ''
  userForm.client_num = row.client_num || '1'
  userForm.expire_user = row.expire_user || ''
  userForm.region_level = row.region_level || 1
  userForm.level_lock = String(row.level_lock) || '2'

  console.log('编辑用户表单数据:', userForm)
  userDialogVisible.value = true
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return

  userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await member_edit(userForm)
        if (res.code === 200) {
          userDialogVisible.value = false
          // 使用Element UI风格的消息提示
          proxy.$message.success(res.msg || '操作成功')
          getList() // 刷新列表
        } else {
          proxy.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        console.error('提交用户数据失败', error)
      }
    } else {
      return false
    }
  })
}

// 删除用户
const handleDeleteUser = (row) => {
  proxy.$confirm(`确认删除用户 ${row.account} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await member_del({ member_id: row.id })
      if (res.code === 200) {
        proxy.$message.success(res.msg || '删除成功')
        getList() // 刷新列表
      } else {
        proxy.$message.error(res.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除用户失败', error)
    }
  }).catch(() => {
    proxy.$message.info('已取消删除')
  })
}

// 格式化订阅方式
const formatSubscribeType = (val) => {
  switch (val) {
    case 1:
    case '1':
      return '未购买'
    case 2:
    case '2':
      return 'Stripe购买'
    case 3:
    case '3':
      return 'Apple订阅'
    default:
      return '-'
  }
}

// 页面加载时获取数据
onMounted(() => {
  // 确保passwordVisible.value是一个对象
  passwordVisible.value = {}
  getChannelList()
  getList()
})
</script>

<template>
  <div class="app-container">
    <!-- 用户列表卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">用户列表</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddUser">添加用户</el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="账号">
            <el-input v-model="searchForm.account" placeholder="请输入账号" clearable></el-input>
          </el-form-item>
          <el-form-item label="渠道">
            <el-select v-model="searchForm.channel_id" placeholder="请选择渠道" clearable>
              <el-option v-for="item in channelOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="限速">
            <el-select v-model="searchForm.speed_limit" placeholder="请选择限速状态" clearable>
              <el-option v-for="item in speedLimitOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="注册类型">
            <el-select v-model="searchForm.invite_type" placeholder="请选择注册类型" clearable>
              <el-option v-for="item in inviteTypeOptions" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="过期时间">
            <el-date-picker v-model="searchForm.ct_start" type="date" placeholder="开始日期" value-format="yyyy-MM-dd"
              style="width: 160px;"></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker v-model="searchForm.ct_end" type="date" placeholder="结束日期" value-format="yyyy-MM-dd"
              style="width: 160px;"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 用户列表 -->
      <div class="list-section">
        <el-table v-loading="loading" :data="tableData" element-loading-text="加载中..." border fit highlight-current-row
          style="width: 100%" height="calc(100vh - 350px)">
          <el-table-column prop="id" label="ID" width="100" align="center"></el-table-column>
          <el-table-column label="账号/邮箱" align="left" min-width="250">
            <template slot-scope="scope">
              <div>账号: {{ scope.row.account }} </div>
              <div>邮箱: {{ scope.row.email }} </div>
              <div>过期:{{ scope.row.expire_user || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态/限速/密码" width="200" align="center">
            <template slot-scope="scope">
              <div>状态:<span :style="{ color: scope.row.status === 1 ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">{{
                formatStatus(scope.row) }}</span> </div>
              <div>限速:<span
                  :style="{ color: scope.row.speed_limit === 1 ? '#67C23A' : '#E6A23C', fontWeight: 'bold' }">{{
                    formatSpeedLimit(scope.row) }}</span> </div>
              <div class="password-container">
                密码：<template v-if="isPasswordVisible(scope.row.id)">
                  <span class="password-text">{{ scope.row.password || '无密码' }}</span>
                </template>
                <template v-else>
                  <span class="password-text">••••••</span>
                </template>
                <el-button type="text" class="password-toggle-btn"
                  @click.prevent.stop="togglePasswordVisibility(scope.row.id)">
                  <i :class="isPasswordVisible(scope.row.id) ? 'el-icon-view' : 'el-icon-lock'"></i>
                </el-button>

              </div>
            </template>
          </el-table-column>
          <el-table-column label="限速值/连接数" align="left" width="130">
            <template slot-scope="scope">
              <div>上行: {{ scope.row.up_speed }} KB/s</div>
              <div>下行: {{ scope.row.down_speed }} KB/s</div>
              <div>连接数: {{ scope.row.client_num }} 个</div>
            </template>
          </el-table-column>
          <el-table-column prop="subscribe" label="订阅类型" width="120" align="center">
            <template slot-scope="scope">
              <div>{{ formatSubscribeType(scope.row.subscribe_type) }}</div>
              <div>{{ formatSubscribe(scope.row.subscribe) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="地区等级/锁定" width="120" align="center">
            <template slot-scope="scope">
              <div>等级: <span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{ scope.row.region_level || 1 }}</span></div>
              <div>锁定: <span :style="{ color: scope.row.level_lock === 1 ? '#F56C6C' : '#67C23A', fontWeight: 'bold' }">{{ formatLevelLock(scope.row.level_lock) }}</span></div>
            </template>
          </el-table-column>
          <el-table-column label="注册信息" width="130" align="left">
            <template slot-scope="scope">
              <div>类型: <span :style="{ color: getInviteTypeColor(scope.row.invite_type), fontWeight: 'bold' }">{{ formatInviteType(scope.row.invite_type) }}</span></div>
              <div v-if="scope.row.upper_show_account">上级: <span :style="{ color: '#E6A23C' }">{{ scope.row.upper_show_account }}</span></div>
              <div v-if="scope.row.upper_show_code">使用码: <span :style="{ color: '#909399' }">{{ scope.row.upper_show_code }}</span></div>
            </template>
          </el-table-column>
          <el-table-column label="邀请统计" width="130" align="center">
            <template slot-scope="scope">
              <div>邀请码: <span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{ scope.row.invite_code || '-' }}</span></div>
              <div>邀请数: <span :style="{ color: getInviteNumColor(scope.row.invite_num), fontWeight: 'bold' }">{{ scope.row.invite_num || 0 }} 人</span></div>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" width="100"></el-table-column>
          <el-table-column label="创建/更新" width="160" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.create_time }}</div>
              {{ scope.row.update_time }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="160">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleEditUser(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="handleDeleteUser(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pagination.page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 用户编辑对话框 -->
    <el-dialog :title="userDialogTitle" :visible.sync="userDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="userFormRef" :model="userForm" :rules="userFormRules" label-width="100px" status-icon>
        <el-form-item label="登录账户" prop="account">
          <el-input v-model="userForm.account" placeholder="请输入登录账户"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="userForm.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="限速" prop="speed_limit">
          <el-radio-group v-model="userForm.speed_limit">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="userForm.speed_limit === '1'">
          <el-form-item label="上行速度" prop="up_speed">
            <el-input v-model="userForm.up_speed" placeholder="请输入上行速度">
              <template slot="append">KB/s</template>
            </el-input>
          </el-form-item>
          <el-form-item label="下行速度" prop="down_speed">
            <el-input v-model="userForm.down_speed" placeholder="请输入下行速度">
              <template slot="append">KB/s</template>
            </el-input>
          </el-form-item>
        </div>
        <el-form-item label="最大连接数" prop="client_num">
          <el-input v-model="userForm.client_num" placeholder="请输入最大连接数"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" placeholder="请输入备注内容" :rows="3"></el-input>
        </el-form-item>
        <el-form-item label="过期时间" prop="expire_user">
          <el-date-picker v-model="userForm.expire_user" type="date" placeholder="选择过期时间" value-format="yyyy-MM-dd"
            style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="地区等级" prop="region_level">
          <el-input-number
            v-model="userForm.region_level"
            :min="1"
            :max="10"
            placeholder="请输入地区等级"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="等级锁定" prop="level_lock">
          <el-radio-group v-model="userForm.level_lock">
            <el-radio label="1">锁定</el-radio>
            <el-radio label="2">不锁定</el-radio>
          </el-radio-group>
          <div class="form-tip">等级锁定后，用户将只能使用当前[地区等级]的节点。</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitUserForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
</style>