<script setup>
import { ref, reactive } from 'vue'
import { channel_withdrew_info, channel_check_withdrew } from '@/api/channel'
import { Message } from 'element-ui'

// 定义emits
const emit = defineEmits(['refresh'])

// 数据
const dialog = ref(false)
const loading = ref(false)
const withdrawInfo = ref({})
const orderList = ref([])
const withdrawId = ref('')
const channelAccount = ref('')
const withdrawAmount = ref('')

// 审核弹窗相关
const checkDialogVisible = ref(false)
const checkLoading = ref(false)
const uploadLoading = ref(false)

// 图片预览弹窗相关
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')
const checkForm = reactive({
  id: '',
  status: '3', // 默认打款成功
  reason: '',
  pic_url: ''
})

// 审核状态选项
const checkStatusOptions = [
  { label: '审核不通过', value: '2' },
  { label: '打款成功', value: '3' }
]

// 打开弹窗
const open = (id, account, amount) => {
  console.log('打开弹窗:', { id, account, amount })
  withdrawId.value = id
  channelAccount.value = account
  withdrawAmount.value = amount
  dialog.value = true
  getWithdrewInfo()
}

// 获取提现详情
const getWithdrewInfo = async () => {
  if (!withdrawId.value) return

  loading.value = true
  try {
    const res = await channel_withdrew_info({ withdrew_id: withdrawId.value })
    if (res.code === 200) {
      withdrawInfo.value = res.data.info || {}
      orderList.value = res.data.lists || []
    }
  } catch (error) {
    console.error('获取提现详情失败', error)
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const close = () => {
  dialog.value = false
  withdrawInfo.value = {}
  orderList.value = []
  withdrawId.value = ''
  channelAccount.value = ''
  withdrawAmount.value = ''
}

// 暴露方法给父组件调用
defineExpose({
  open
})

// 格式化金额（分转元）
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0.00'
  const yuan = parseFloat(amount) / 100
  return yuan.toFixed(2)
}

// 获取金额颜色
const getAmountColor = (amount) => {
  const yuan = parseFloat(amount) / 100 || 0
  if (yuan >= 100) {
    return '#67C23A' // 绿色 - 高金额 (>=100元)
  } else if (yuan >= 50) {
    return '#E6A23C' // 橙色 - 中等金额 (50-99元)
  } else if (yuan > 0) {
    return '#409EFF' // 蓝色 - 低金额 (>0元)
  } else {
    return '#909399' // 灰色 - 无金额 (0元)
  }
}

// 格式化商品名称
const formatProductName = (productId) => {
  if (!productId) return '-'
  if (productId.startsWith('Surfelves')) {
    return productId.substring(9)
  }
  return productId
}

// 格式化订单类型
const formatPayType = (payType) => {
  switch (payType) {
    case 1:
      return 'Stripe'
    case 2:
      return 'Apple'
    default:
      return '未知'
  }
}

// 格式化返利状态
const formatRebateStatus = (status) => {
  switch (status) {
    case 1:
      return '购买返利'
    case 5:
      return '退款扣返利'
    default:
      return '未知'
  }
}

// 获取返利状态颜色
const getRebateStatusColor = (status) => {
  switch (status) {
    case 1:
      return '#67C23A' // 绿色 - 购买返利
    case 5:
      return '#F56C6C' // 红色 - 退款扣返利
    default:
      return '#909399' // 灰色 - 未知
  }
}

// 格式化提现状态
const formatWithdrewStatus = (status) => {
  switch (status) {
    case 1:
      return '未提现'
    case 2:
      return '审核中'
    case 3:
      return '已提现'
    default:
      return '未知'
  }
}

// 获取提现状态颜色
const getWithdrewStatusColor = (status) => {
  switch (status) {
    case 1:
      return '#E6A23C' // 橙色 - 未提现
    case 2:
      return '#409EFF' // 蓝色 - 审核中
    case 3:
      return '#67C23A' // 绿色 - 已提现
    default:
      return '#909399' // 灰色 - 未知
  }
}

// 格式化提现状态
const formatStatus = (status) => {
  switch (status) {
    case 1:
      return '未审核'
    case 2:
      return '审核不通过'
    case 3:
      return '打款成功'
    default:
      return '未知'
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1:
      return '#E6A23C' // 橙色 - 未审核
    case 2:
      return '#F56C6C' // 红色 - 审核不通过
    case 3:
      return '#67C23A' // 绿色 - 打款成功
    default:
      return '#909399' // 灰色 - 未知
  }
}

// 查看图片 - 在本页面显示
const viewImage = (imageUrl) => {
  if (imageUrl) {
    previewImageUrl.value = imageUrl
    imagePreviewVisible.value = true
  }
}

// 关闭图片预览
const closeImagePreview = () => {
  imagePreviewVisible.value = false
  previewImageUrl.value = ''
}

// 打开审核弹窗
const handleCheck = () => {
  checkForm.id = withdrawInfo.value.id || withdrawId.value
  checkForm.status = '3'
  checkForm.reason = ''
  checkForm.pic_url = ''
  checkDialogVisible.value = true
}

// 关闭审核弹窗
const handleCheckClose = () => {
  checkDialogVisible.value = false
}

// 文件上传 - 直接展示图片内容，不调用上传接口
const handleUpload = (file) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    console.error('只能上传图片文件')
    Message.error('只能上传图片文件')
    return false
  }

  // 检查文件大小 (限制为5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    console.error('图片大小不能超过5MB')
    Message.error('图片大小不能超过5MB')
    return false
  }

  uploadLoading.value = true

  // 压缩图片并转换为 base64，确保最终大小不超过60KB
  compressImageToTarget(file, 60).then(compressedBase64 => {
    console.log('图片压缩完成')
    checkForm.pic_url = compressedBase64
    uploadLoading.value = false

    // 显示压缩效果
    const originalSize = Math.round(file.size / 1024)
    const compressedSize = Math.round(compressedBase64.length * 0.75 / 1024) // base64比原始数据大约33%
    console.log(`图片压缩: ${originalSize}KB -> ${compressedSize}KB (压缩率: ${Math.round((1 - compressedSize / originalSize) * 100)}%)`)
  }).catch(error => {
    console.error('图片压缩失败:', error)
    uploadLoading.value = false
  })

  return false // 阻止默认上传行为
}

// 压缩图片到目标大小
const compressImageToTarget = async (file, targetSizeKB = 80) => {
  try {
    // 第一次压缩：使用标准参数
    let compressedBase64 = await compressImage(file, 0.7, 1000, 800)
    let currentSizeKB = Math.round(compressedBase64.length * 0.75 / 1024)

    console.log(`第一次压缩: ${Math.round(file.size / 1024)}KB -> ${currentSizeKB}KB`)

    // 如果还是超过目标大小，进行二次压缩
    if (currentSizeKB > targetSizeKB) {
      console.log(`图片大小 ${currentSizeKB}KB 超过目标 ${targetSizeKB}KB，进行二次压缩...`)

      // 计算需要的压缩比例
      const compressionRatio = targetSizeKB / currentSizeKB
      let newQuality = Math.max(0.3, 0.7 * compressionRatio) // 质量不低于30%
      let newMaxWidth = Math.max(400, Math.round(1000 * Math.sqrt(compressionRatio))) // 宽度不小于400px
      let newMaxHeight = Math.max(300, Math.round(800 * Math.sqrt(compressionRatio))) // 高度不小于300px

      compressedBase64 = await compressImage(file, newQuality, newMaxWidth, newMaxHeight)
      currentSizeKB = Math.round(compressedBase64.length * 0.75 / 1024)

      console.log(`二次压缩: 质量${Math.round(newQuality * 100)}%, 尺寸${newMaxWidth}x${newMaxHeight}, 大小${currentSizeKB}KB`)

      // 如果还是超过，进行第三次更激进的压缩
      if (currentSizeKB > targetSizeKB) {
        console.log(`仍超过目标大小，进行第三次压缩...`)
        newQuality = Math.max(0.2, newQuality * 0.7)
        newMaxWidth = Math.max(300, Math.round(newMaxWidth * 0.8))
        newMaxHeight = Math.max(200, Math.round(newMaxHeight * 0.8))

        compressedBase64 = await compressImage(file, newQuality, newMaxWidth, newMaxHeight)
        currentSizeKB = Math.round(compressedBase64.length * 0.75 / 1024)

        console.log(`第三次压缩: 质量${Math.round(newQuality * 100)}%, 尺寸${newMaxWidth}x${newMaxHeight}, 大小${currentSizeKB}KB`)
      }
    }

    return compressedBase64
  } catch (error) {
    throw new Error('图片压缩失败: ' + error.message)
  }
}

// 图片压缩函数
const compressImage = (file, quality = 0.7, maxWidth = 1000, maxHeight = 800) => {
  return new Promise((resolve, reject) => {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      reject(new Error('不是有效的图片文件'))
      return
    }

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      try {
        // 计算压缩后的尺寸
        let { width, height } = img

        // 如果图片尺寸小于最大限制，保持原尺寸
        if (width <= maxWidth && height <= maxHeight) {
          // 小图片也进行质量压缩
          canvas.width = width
          canvas.height = height
          ctx.drawImage(img, 0, 0, width, height)
        } else {
          // 按比例缩放大图片
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width = Math.round(width * ratio)
          height = Math.round(height * ratio)

          canvas.width = width
          canvas.height = height

          // 使用高质量的图片缩放
          ctx.imageSmoothingEnabled = true
          ctx.imageSmoothingQuality = 'medium'
          ctx.drawImage(img, 0, 0, width, height)
        }

        // 转换为 base64，使用 JPEG 格式和指定质量
        const compressedBase64 = canvas.toDataURL('image/jpeg', quality)
        resolve(compressedBase64)
      } catch (error) {
        reject(new Error('图片处理失败: ' + error.message))
      }
    }

    img.onerror = () => {
      reject(new Error('图片加载失败'))
    }

    // 读取文件并设置图片源
    const reader = new FileReader()
    reader.onload = (e) => {
      img.src = e.target.result
    }
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    reader.readAsDataURL(file)
  })
}

// 图片加载错误处理
const handleImageError = () => {
  console.error('图片加载失败')
}

// 文件选择变化事件
const handleFileChange = (file, fileList) => {
  console.log('文件选择变化:', file, fileList)
  if (file && file.raw) {
    handleUpload(file.raw)
  }
}

// 文件数量超出限制
const handleExceed = () => {
  console.log('文件数量超出限制')
}



// 提交审核
const submitCheck = async () => {
  if (!checkForm.id) {
    console.error('缺少提现ID')
    return
  }

  // 审核不通过时必须填写原因
  if (checkForm.status === '2' && !checkForm.reason) {
    console.error('审核不通过时必须填写原因')
    return
  }

  // 打款成功时建议上传打款凭据（非必需）
  if (checkForm.status === '3' && !checkForm.pic_url) {
    console.error('打款成功时必须上传打款凭据')
    return
  }

  checkLoading.value = true
  try {
    const res = await channel_check_withdrew({
      withdrew_id: checkForm.id,
      status: checkForm.status,
      reason: checkForm.reason,
      pic_data: checkForm.pic_url
    })

    if (res.code === 200) {
      console.log('审核成功')
      Message.success('审核成功')
      checkDialogVisible.value = false
      // 重新获取详情数据
      getWithdrewInfo()
      // 通知父组件刷新列表
      emit('refresh')
    } else {
      Message.error(res.msg || '审核失败')
      console.error('审核失败:', res.msg)
    }
  } catch (error) {
    console.error('审核失败', error)
  } finally {
    checkLoading.value = false
  }
}
</script>

<template>
  <div>
    <el-dialog :title="`ID:${withdrawId}-${channelAccount} (¥${formatAmount(withdrawAmount)}) 的提现详情`"
      :visible.sync="dialog" :before-close="close" width="1200px" :close-on-click-modal="false" :append-to-body="true">
      <div v-loading="loading" class="withdrew-detail">
        <!-- 提现信息头部 -->
        <el-card class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <div class="header-info">
              <span class="title">提现信息</span>
              <div class="info-items">
                <div class="info-item">
                  <i class="el-icon-user"></i>
                  <span class="label">账号:</span>
                  <span class="value account">{{ channelAccount }}</span>
                </div>
                <div class="info-item">
                  <i class="el-icon-money"></i>
                  <span class="label">金额:</span>
                  <span class="value amount" :style="{ color: getAmountColor(withdrawInfo.amount) }">
                    ¥{{ formatAmount(withdrawInfo.amount) }}
                  </span>
                </div>
                <div class="info-item">
                  <i class="el-icon-document"></i>
                  <span class="label">关联订单:</span>
                  <span class="value">{{ withdrawInfo.order_count || 0 }} 条</span>
                </div>
                <div class="info-item">
                  <i class="el-icon-time"></i>
                  <span class="label">创建时间:</span>
                  <span class="value">{{ withdrawInfo.create_time || '-' }}</span>
                </div>
                <div v-if="withdrawInfo.status != 1" class="info-item">
                  <i class="el-icon-time"></i>
                  <span class="label">审核时间:</span>
                  <span class="value">{{ withdrawInfo.update_time || '-' }}</span>
                </div>

              </div>
            </div>
          </div>
          <div class="info-content">
            <!-- 审核状态、备注信息、凭据信息 在一行简单展示 -->
            <div class="simple-info-row">
              <div class="info-section">
                <span class="label">审核状态：</span>
                <el-tag size="small" :type="withdrawInfo.status === 1 ? 'warning' : (withdrawInfo.status === 2 ? 'danger' : 'success')">
                  {{ formatStatus(withdrawInfo.status) }}
                </el-tag>
              </div>
              <div class="info-section">
                <span class="label">凭据信息：</span>
                <el-button v-if="withdrawInfo.pic_url" size="mini" icon="el-icon-view" type="text" @click="viewImage(withdrawInfo.pic_url)">
                  查看凭据
                </el-button>
                <span v-else class="content">暂无凭据</span>
              </div>
              <div class="info-section">
                <span class="label">备注信息：</span>
                <span class="content" style="max-width: 600px;">{{ withdrawInfo.reason || '暂无备注' }}</span>
              </div>
            </div>

            <!-- 审核按钮区域 -->
            <div v-if="withdrawInfo.status == 1" class="action-section">
              <el-button type="primary" size="small" @click="handleCheck">开始审核</el-button>
            </div>
          </div>
        </el-card>

        <!-- 关联订单列表 -->
        <el-card class="order-card" shadow="never" style="margin-top: 20px;">
          <div slot="header" class="card-header">
            <span>关联订单列表</span>
            <span class="order-count">(共 {{ orderList.length }} 条)</span>
          </div>
          <el-table :data="orderList" border style="width: 100%" max-height="400">
            <el-table-column prop="id" label="ID" width="60" align="center"></el-table-column>
            <el-table-column prop="maccount" label="用户账号" align="center" min-width="120"></el-table-column>
            <el-table-column label="订单产品" align="center" min-width="120">
              <template slot-scope="scope">
                <div>{{ formatPayType(scope.row.pay_type) }}</div>
                <span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{ formatProductName(scope.row.product_id)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="订单金额" align="center" width="120">
              <template slot-scope="scope">
                <span :style="{ color: getAmountColor(scope.row.pay_amount), fontWeight: 'bold' }">¥{{
                  formatAmount(scope.row.pay_amount) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="返利金额" align="center" width="120">
              <template slot-scope="scope">
                <span
                  :style="{ color: scope.row.rebate_amount < 0 ? '#F56C6C' : getAmountColor(scope.row.rebate_amount), fontWeight: 'bold' }">
                  {{ scope.row.rebate_amount < 0 ? '-' : '' }}¥{{ formatAmount(Math.abs(scope.row.rebate_amount)) }}
                    </span>
              </template>
            </el-table-column>
            <el-table-column label="返利状态" align="center" width="120">
              <template slot-scope="scope">
                <span :style="{ color: getRebateStatusColor(scope.row.status), fontWeight: 'bold' }">{{
                  formatRebateStatus(scope.row.status) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="提现状态" align="center" width="100">
              <template slot-scope="scope">
                <span :style="{ color: getWithdrewStatusColor(scope.row.withdrew_status), fontWeight: 'bold' }">{{
                  formatWithdrewStatus(scope.row.withdrew_status) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="rebate_day" label="预计返利日期" align="center" width="120"></el-table-column>
            <el-table-column prop="create_time" label="创建时间" align="center" width="160"></el-table-column>
          </el-table>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog title="提现审核" :visible.sync="checkDialogVisible" @close="handleCheckClose" width="500px"
      :close-on-click-modal="false">
      <el-form :model="checkForm" label-width="100px">
        <el-form-item label="审核结果" required>
          <el-radio-group v-model="checkForm.status">
            <el-radio v-for="option in checkStatusOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="checkForm.status === '2' || checkForm.status === '3'" label="审核原因">
          <el-input v-model="checkForm.reason" type="textarea" :rows="3"
            :placeholder="checkForm.status === '2' ? '请输入审核不通过的原因' : '请输入备注信息'"></el-input>
        </el-form-item>



        <el-form-item v-if="checkForm.status === '2' || checkForm.status === '3'" label="打款凭据">
          <div>
            <el-upload action="#" :before-upload="handleUpload" :show-file-list="false" accept="image/*"
              :on-change="handleFileChange" :on-exceed="handleExceed" :limit="1">
              <el-button :loading="uploadLoading" size="small" type="primary">
                {{ uploadLoading ? '上传中...' : '上传凭据' }}
              </el-button>
            </el-upload>

            <!-- 图片预览区域 -->
            <div v-if="checkForm.pic_url && checkForm.pic_url.length > 0" style="margin-top: 10px;">
              <div style="margin-bottom: 8px;">
                <img :src="checkForm.pic_url" alt="打款凭据"
                  style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; display: block;"
                  @click="viewImage(checkForm.pic_url)" @error="handleImageError" />
              </div>
              <el-button size="mini" type="text" @click="viewImage(checkForm.pic_url)">
                点击查看大图
              </el-button>
            </div>

            <!-- 调试信息 -->
            <div style="margin-top: 5px; font-size: 12px; color: #999;">
              状态: {{ checkForm.pic_url ? '已上传图片' : '未上传图片' }}
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCheckClose">取消</el-button>
        <el-button type="primary" :loading="checkLoading" @click="submitCheck">
          {{ checkLoading ? '提交中...' : '提交审核' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog title="查看凭据" :visible.sync="imagePreviewVisible" @close="closeImagePreview" width="80%"
      :close-on-click-modal="true" :append-to-body="true" center>
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="打款凭据" class="preview-image" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeImagePreview">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.withdrew-detail {

  .info-card,
  .order-card {
    .card-header {
      font-weight: bold;
      color: #303133;

      .header-info {
        display: flex;
        align-items: center;
        gap: 20px;

        .title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-right: 10px;
        }

        .info-items {
          display: flex;
          align-items: center;
          gap: 25px;
          flex: 1;

          .info-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border-radius: 6px;
            border: 1px solid #e1e8ff;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }

            i {
              font-size: 14px;
              color: #409eff;
            }

            .label {
              font-size: 12px;
              color: #666;
              font-weight: 500;
            }

            .value {
              font-size: 13px;
              font-weight: 600;
              color: #303133;

              &.account {
                color: #409eff;
              }

              &.amount {
                font-weight: bold;
                font-size: 14px;
              }
            }
          }
        }
      }

      .order-count {
        color: #909399;
        font-weight: normal;
        margin-left: 8px;
      }
    }
  }

  .info-content {
    .simple-info-row {
      display: flex;
      align-items: flex-start;
      gap: 30px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .info-section {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        .label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          white-space: nowrap;
        }

        .content {
          font-size: 14px;
          color: #333;
          max-width: 600px;
          word-wrap: break-word;
          word-break: break-all;
          line-height: 1.4;
        }
      }
    }

    .action-section {
      margin-top: 15px;
      text-align: right;
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 图片预览样式
.image-preview-container {
  text-align: center;
  padding: 20px;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>