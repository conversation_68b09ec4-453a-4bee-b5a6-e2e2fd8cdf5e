<template>
  <div class="app-container">
    <!-- 订单配置卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">订单配置</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddOrder">添加订单配置</el-button>
        </div>
      </div>

      <!-- 订单配置列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="orderList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" align="center" width="80"></el-table-column>
          <el-table-column prop="product_id" label="苹果内购ID" align="center"></el-table-column>
          <el-table-column prop="amount" label="金额(元)" align="center" :formatter="formatAmount"></el-table-column>
          <el-table-column prop="months" label="时长(月)" align="center"></el-table-column>
          <el-table-column label="状态" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="120">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditOrder(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 订单编辑对话框 -->
    <el-dialog
      :title="orderDialogTitle"
      :visible.sync="orderDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="苹果内购ID" prop="product_id">
          <el-input v-model="orderForm.product_id" placeholder="请输入苹果内购ID"></el-input>
        </el-form-item>
        <el-form-item label="商品金额(分)" prop="amount">
          <el-input v-model="orderForm.amount" type="number" placeholder="请输入商品金额(分)"></el-input>
        </el-form-item>
        <el-form-item label="购买时长(月)" prop="months">
          <el-input v-model="orderForm.months" type="number" placeholder="请输购买时长(月)"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="orderForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitOrderForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { system_orders, system_order_edit } from '@/api/sys'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义表格数据
const orderList = ref([])
const loading = ref(false)

// 订单表单对话框
const orderDialogVisible = ref(false)
const orderDialogTitle = ref('')
const orderForm = reactive({
  order_id: 0,
  product_id: '',
  amount: 0,
  months: 1,
  status: 1
})

// 表单验证规则
const orderFormRules = {
  product_id: [
    { required: true, message: '请输入苹果内购ID', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入商品金额(分)', trigger: 'blur' }
  ],
  months: [
    { required: true, message: '请输购买时长(月)', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 订单表单ref
const orderFormRef = ref(null)
const submitLoading = ref(false)

// 获取订单列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await system_orders({})
    if (res.code === 200) {
      orderList.value = res.data.lists
    }
  } catch (error) {
    console.error('Error fetching orders:', error)
  } finally {
    loading.value = false
  }
}

// 格式化金额
const formatAmount = (row, column, cellValue) => {
  // 将分转换为元，保留两位小数
  return (cellValue / 100).toFixed(2)
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp || timestamp === 0) return '暂无'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 打开添加订单对话框
const handleAddOrder = () => {
  orderDialogTitle.value = '新增订单配置'
  // 重置表单
  Object.keys(orderForm).forEach(key => {
    if (key === 'order_id') {
      orderForm[key] = 0
    } else if (key === 'months') {
      orderForm[key] = 1
    } else if (key === 'status') {
      orderForm[key] = 1
    } else if (key === 'amount') {
      orderForm[key] = 0
    } else {
      orderForm[key] = ''
    }
  })
  orderDialogVisible.value = true
}

// 打开编辑订单对话框
const handleEditOrder = (row) => {
  orderDialogTitle.value = '编辑订单配置'
  // 填充表单数据
  orderForm.order_id = row.id || 0
  orderForm.product_id = row.product_id || ''
  orderForm.amount = row.amount || 0
  orderForm.months = row.months || 1
  orderForm.status = row.status || 1

  orderDialogVisible.value = true
}

// 提交订单表单
const submitOrderForm = async () => {
  if (!orderFormRef.value) return

  orderFormRef.value.validate(async (valid) => {
    if (!valid) {
      return false
    }

    submitLoading.value = true
    try {
      const params = {
        order_id: orderForm.order_id || 0,
        product_id: orderForm.product_id,
        amount: orderForm.amount,
        months: orderForm.months,
        status: orderForm.status
      }

      const res = await system_order_edit(params)
      if (res.code === 200) {
        orderDialogVisible.value = false
        getList() // 刷新列表
      }
    } catch (error) {
      console.error('Error submitting form:', error)
    } finally {
      submitLoading.value = false
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
