<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { channel_list, channel_edit } from '@/api/channel'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  account: '',
  status: '',
  remark: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const tableData = ref([])
const loading = ref(false)

// 密码可见性状态 - 初始化为空对象
const passwordVisible = ref({})

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: '1' },
  { label: '禁用', value: '2' }
]

// 渠道表单对话框
const channelDialogVisible = ref(false)
const channelDialogTitle = ref('')
const channelForm = reactive({
  channel_id: '0',
  account: '',
  password: '',
  auth_secret: '',
  status: '1',
  rebate_rate: 0,
  remark: ''
})

// 表单验证规则
const channelFormRules = {
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  auth_secret: [{ required: true, message: '请输入认证密钥', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  rebate_rate: [
    { required: true, message: '请输入返佣比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '返佣比例必须在0-100之间', trigger: 'blur' }
  ]
}

// 渠道表单ref
const channelFormRef = ref(null)

// 获取渠道列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await channel_list(params)
    tableData.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page

    // 重置密码可见性 - 确保是一个对象
    passwordVisible.value = {}
  } catch (error) {
    console.error('获取渠道列表失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 格式化状态
const formatStatus = (row) => {
  return row.status === 1 ? '启用' : '禁用'
}

// 格式化金额（分转元）
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0.00'
  // 将分转换为元，除以100
  const yuan = parseFloat(amount) / 100
  return yuan.toFixed(2)
}

// 获取金额颜色（按元计算）
const getAmountColor = (amount) => {
  // 将分转换为元进行颜色判断
  const yuan = parseFloat(amount) / 100 || 0
  if (yuan >= 1000) {
    return '#67C23A' // 绿色 - 高金额 (>=1000元)
  } else if (yuan >= 100) {
    return '#E6A23C' // 橙色 - 中等金额 (100-999元)
  } else if (yuan > 0) {
    return '#409EFF' // 蓝色 - 低金额 (>0元)
  } else {
    return '#909399' // 灰色 - 无金额 (0元)
  }
}

// 检查密码是否显示
const isPasswordVisible = (id) => {
  return passwordVisible.value && passwordVisible.value[id] === true
}

// 切换密码显示状态
const togglePasswordVisibility = (id) => {
  // 确保passwordVisible.value是一个对象
  if (!passwordVisible.value) {
    passwordVisible.value = {}
  }

  // 使用Vue的方式设置对象属性，确保响应式更新
  passwordVisible.value = {
    ...passwordVisible.value,
    [id]: !passwordVisible.value[id]
  }
}

// 打开添加渠道对话框
const handleAddChannel = () => {
  channelDialogTitle.value = '新增渠道'
  // 重置表单
  Object.keys(channelForm).forEach(key => {
    if (key === 'channel_id') {
      channelForm[key] = '0'
    } else if (key === 'status') {
      channelForm[key] = '1'
    } else if (key === 'rebate_rate') {
      channelForm[key] = 0
    } else {
      channelForm[key] = ''
    }
  })
  channelDialogVisible.value = true
}

// 打开编辑渠道对话框
const handleEditChannel = (row) => {
  channelDialogTitle.value = '编辑渠道'
  // 填充表单数据
  channelForm.channel_id = row.id || 0
  channelForm.account = row.account || ''
  channelForm.password = row.password || ''
  channelForm.auth_secret = row.auth_secret || ''
  channelForm.status = String(row.status) || '1'
  channelForm.rebate_rate = row.rebate_rate || 0
  channelForm.remark = row.remark || ''

  channelDialogVisible.value = true
}

// 提交渠道表单
const submitChannelForm = async () => {
  if (!channelFormRef.value) return

  channelFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await channel_edit(channelForm)
        if (res.code === 200) {
          channelDialogVisible.value = false
          getList() // 刷新列表
        }
      } catch (error) {
        console.error('提交渠道数据失败', error)
      }
    } else {
      return false
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  // 确保passwordVisible.value是一个对象
  passwordVisible.value = {}
  getList()
})
</script>

<template>
  <div class="app-container">
    <!-- 渠道管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">渠道列表</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddChannel">添加渠道</el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="账号">
            <el-input v-model="searchForm.account" placeholder="请输入账号" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="searchForm.remark" placeholder="请输入备注" clearable></el-input>
          </el-form-item>
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="searchForm.ct_start"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="searchForm.ct_end"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 160px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 渠道列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="tableData"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="50" align="center"></el-table-column>
          <el-table-column prop="account" label="账号" align="center"></el-table-column>
          <el-table-column label="密码" align="center" width="120">
            <template slot-scope="scope">
              <div class="password-container">
                <template v-if="isPasswordVisible(scope.row.id)">
                  <span class="password-text">{{ scope.row.password || '无密码' }}</span>
                </template>
                <template v-else>
                  <span class="password-text">••••••</span>
                </template>
                <el-button
                  type="text"
                  class="password-toggle-btn"
                  @click.prevent.stop="togglePasswordVisibility(scope.row.id)"
                >
                  <i :class="isPasswordVisible(scope.row.id) ? 'el-icon-view' : 'el-icon-lock'"></i>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="invite_num" label="邀请人数" width="100" align="center"></el-table-column>
          <el-table-column label="返佣比例" width="100" align="center">
            <template slot-scope="scope">
              <span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{ scope.row.rebate_rate || 0 }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="提现金额" width="150" align="center">
            <template slot-scope="scope">
              <div>已提现: <span :style="{ color: getAmountColor(scope.row.withdraw), fontWeight: 'bold' }">¥{{ formatAmount(scope.row.withdraw) }}</span></div>
              <div>待提现: <span :style="{ color: getAmountColor(scope.row.unwithdrew), fontWeight: 'bold' }">¥{{ formatAmount(scope.row.unwithdrew) }}</span></div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ formatStatus(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最后登录信息" width="185" align="center">
            <template slot-scope="scope">
              <div>IP: <span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{ scope.row.last_ip || '-' }}</span></div>
              <div>时间: <span :style="{ color: '#909399' }">{{ scope.row.last_time }}</span></div>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
          <el-table-column label="创建/更新时间" width="180" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.create_time }}</div>
              <div>{{ scope.row.update_time }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="160">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditChannel(scope.row)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 渠道编辑对话框 -->
    <el-dialog
      :title="channelDialogTitle"
      :visible.sync="channelDialogVisible"
      width="550px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="channelFormRef"
        :model="channelForm"
        :rules="channelFormRules"
        label-width="100px"
        status-icon
      >
        <el-form-item label="账号" prop="account">
          <el-input v-model="channelForm.account" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="channelForm.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="认证密钥" prop="auth_secret">
          <el-input v-model="channelForm.auth_secret" placeholder="请输入认证密钥"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="channelForm.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="返佣比例" prop="rebate_rate">
          <el-input-number
            v-model="channelForm.rebate_rate"
            :min="0"
            :max="100"
            placeholder="请输入返佣比例"
            style="width:60%"
          ></el-input-number>
          <span style="margin-left: 8px; color: #909399;">%</span>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="channelForm.remark"
            type="textarea"
            placeholder="请输入备注内容"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="channelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitChannelForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
