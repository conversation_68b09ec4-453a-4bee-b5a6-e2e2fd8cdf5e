<template>
  <div class="app-container">
    <!-- 服务器日志卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">服务器日志</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
          </el-form-item>
          <el-form-item label="主机">
            <el-input v-model="searchForm.host" placeholder="请输入主机地址" clearable></el-input>
          </el-form-item>
          <el-form-item label="远程IP">
            <el-input v-model="searchForm.remote_ip" placeholder="请输入远程IP地址" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 服务器日志列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="logList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="username" label="用户名" align="center"></el-table-column>
          <el-table-column prop="token_id" label="Token_Id" align="center"></el-table-column>
          <el-table-column prop="country_code" label="国家代码" align="center"></el-table-column>
          <el-table-column prop="server_num" label="服务器编号" align="center"></el-table-column>
          <el-table-column prop="host" label="主机" align="center"></el-table-column>
          <el-table-column prop="remote_ip" label="远程IP" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { server_log } from '@/api/server'

// 定义搜索表单数据
const searchForm = reactive({
  username: '',
  host: '',
  remote_ip: ''
})

// 定义表格数据
const logList = ref([])
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 获取服务器日志列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await server_log(params)
    logList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取服务器日志失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
