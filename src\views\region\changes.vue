<template>
  <div class="app-container">
    <!-- 中转服务器卡片 -->
    <el-card shadow="hover" class="changes-card">
      <div slot="header" class="clearfix">
        <span class="title-before">中转服务器列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAddRelay">添加中转服务器</el-button>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="地区">
            <el-select v-model="searchForm.region_id" placeholder="请选择地区" clearable>
              <el-option
                v-for="item in regionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="IP">
            <el-input v-model="searchForm.ip" placeholder="请输入IP" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearchForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 中转服务器列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="changesList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" align="center" width="70" />
          <el-table-column label="地区" align="center" min-width="160">
            <template slot-scope="scope">
              <world-icon
                v-if="getRegionInfo(scope.row.region_id)?.region_icon"
                :icon="`word${getRegionInfo(scope.row.region_id)?.region_icon}`"
                :size="24"
                class="region-icon"
              />
              {{ getRegionInfo(scope.row.region_id)?.cn_name || '--' }}
              <div>GUID:{{ scope.row.guid || '--' }}</div>
            </template>
          </el-table-column>
            <el-table-column label="IP" align="center" >
              <template slot-scope="scope">
                  <div>{{ scope.row.ip }}:{{ scope.row.port}}</div>
              </template>
            </el-table-column>
            <el-table-column label="密码/加密方式" align="center" width="160" >
              <template slot-scope="scope">
                  <div>PWD:{{ scope.row.password || '' }}</div>
                  <div>Method:{{ scope.row.method || '' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="连接数/CPU" align="center" width="120">
              <template slot-scope="scope">
                  <div>connect:{{ scope.row.connect_num || 0 }}</div>
                  <div>CPU:{{ scope.row.cup || 0.00 }}%</div>
              </template>
            </el-table-column>
            <el-table-column label="带宽/内存(M)" align="center" width="130">
              <template slot-scope="scope">
                  <div>{{ scope.row.broadband || 0 }}M / {{ scope.row.use_broadband || 0.00 }}%</div>
                  <div>{{ scope.row.memory || 0 }}M / {{ scope.row.use_memory || 0.00 }}%</div>
              </template>
            </el-table-column>
            <el-table-column label="预估权重" align="center" width="150">
              <template slot="header">
              <div style="display: flex; align-items: center; justify-content: center;">
                <span>预估权重</span>
                <i class="el-icon-question" style="margin-left: 5px; cursor: pointer;" @click.stop="showWeightInfo"></i>
              </div>
            </template>
              <template slot-scope="scope">
                <div class="weight-container">
                  <span>{{ scope.row.weight }}</span>
                  <i class="el-icon-s-data chart-icon" @click.stop="showChangesChart(scope.row)" title="查看性能图表"></i>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态/心跳" align="center" width="160">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' :
                 scope.row.status === 2 ? 'danger' :
                 scope.row.status === 3 ? 'info' : 'warning'">
                {{ scope.row.status === 1 ? '启用' :
                  scope.row.status === 2 ? '禁用' :
                  scope.row.status === 3 ? '离线' : '未知状态' }}
              </el-tag>
                <div>{{ scope.row.last_time }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" fixed="right" width="160">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >编辑</el-button>
                <el-button
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

      <!-- 编辑对话框 -->
      <el-dialog
        :visible.sync="editDialog"
        :title="editForm.change_id ? '编辑中转服务器' : '添加中转服务器'"
        :append-to-body="true"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form ref="editForm" :model="editForm" :rules="rules" label-width="80px">
          <el-form-item label="所属地区">
            <el-select v-model="editForm.region_id" placeholder="请选择地区" disabled>
              <el-option
                v-for="item in regionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="GUID" prop="guid">
            <el-input v-model="editForm.guid" placeholder="请输入中转服务器的GUID"></el-input>
          </el-form-item>
          <el-form-item label="IP" prop="ip">
            <el-input v-model="editForm.ip" placeholder="请输入IP"></el-input>
          </el-form-item>
          <el-form-item label="端口号" prop="port">
            <el-input v-model="editForm.port" placeholder="请输入端口号"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="editForm.password" placeholder="请输入密码"></el-input>
          </el-form-item>
          <el-form-item label="加密方式" prop="method">
            <el-select v-model="editForm.method" placeholder="请选择加密方式">
              <el-option
                v-for="item in proxyMethods"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="带宽(M)" prop="broadband">
            <el-input type="number" v-model="editForm.broadband" placeholder="请输带宽(M)">
              <template slot="append">M</template>
            </el-input>
          </el-form-item>
          <el-form-item label="内存(M)" prop="memory">
            <el-input type="number" v-model="editForm.memory" placeholder="请输内存(M)">
              <template slot="append">M</template>
            </el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="editForm.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="2">禁用</el-radio>
              <el-radio :label="3">离线</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeEditDialog">取 消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitEditForm">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 性能图表弹窗 -->
      <el-dialog
        title="中转服务器性能监控"
        :visible.sync="chartVisible"
        width="800px"
        center
        :append-to-body="true"
        :before-close="closeChartDialog"
        :close-on-click-modal="false"
      >
        <div v-loading="chartLoading" class="chart-container">
          <div v-if="selectedServer" class="server-info">
            <p><strong>中转服务器:</strong> {{ selectedServer.ip }}:{{ selectedServer.port }} ({{ selectedServer.guid }})</p>
          </div>
          <div id="performanceChart" class="chart"></div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="closeChartDialog">关闭</el-button>
        </span>
      </el-dialog>

      <!-- 预估权重信息弹窗 -->
    <el-dialog
      title="预估权重计算公式"
      :visible.sync="weightInfoVisible"
      width="500px"
      center
      :close-on-click-modal="false"
    >
      <div class="weight-info">
        <p>预估权重 = 0.42 × (时间窗口流量 / 最大时间窗口流量)</p>
        <p>+ 0.25 × (时间窗口连接数 / 最大时间窗口连接数固定 1000)</p>
        <p>+ 0.17 × (CPU使用率)</p>
        <p>+ 0.17 × (内存使用率)</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="weightInfoVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 地区选择对话框 -->
    <el-dialog
      title="选择地区"
      :visible.sync="regionSelectVisible"
      width="400px"
      center
      :close-on-click-modal="false"
    >
      <el-form>
        <el-form-item label="选择地区">
          <el-select v-model="selectedRegionId" placeholder="请选择地区" style="width: 100%">
            <el-option
              v-for="item in regionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="regionSelectVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmRegionSelect">确 定</el-button>
      </span>
    </el-dialog>
    </div>
  </template>

  <script>
  import { region_changes, region_changes_edit, region_changes_del, region_changes_echarts, region_list } from '@/api/server'
  import * as echarts from 'echarts'

  export default {
    data() {
      return {
        editDialog: false,
        region_id: null,
        regin_name: '',
        cn_name: '',
        changesList: [],
        loading: false,
        submitLoading: false,
        passwordVisible: {},
        // 分页相关
        currentPage: 1,
        pageSize: 20,
        total: 0,
        // 地区列表
        regionOptions: [],
        // 地区信息映射
        regionMap: {},
        // 搜索表单
        searchForm: {
          region_id: '',
          status: '',
          ip: ''
        },
        // 状态选项
        statusOptions: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 2 },
          { label: '离线', value: 3 }
        ],
        // 地区选择对话框
        regionSelectVisible: false,
        selectedRegionId: '',
        editForm: {
          change_id: null,
          guid: '',
          ip: '',
          port: '',
          password: '',
          method: '',
          status: 1,
          broadband: 0,
          memory: 0
        },
        rules: {
          guid: [
            { required: true, message: '请输入GUID', trigger: 'blur' }
          ],
          ip: [
            { required: true, message: '请输入IP', trigger: 'blur' }
          ],
          port: [
            { required: true, message: '请输入端口号', trigger: 'blur' }
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' }
          ],
          method: [
            { required: true, message: '请选择加密方式', trigger: 'change' }
          ],
          broadband: [
            { required: true, message: '请输入带宽(M)', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value <= 0) {
                  callback(new Error('带宽必须大于0'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          memory: [
            { required: true, message: '请输入内存(M)', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value <= 0) {
                  callback(new Error('内存必须大于0'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ],
          status: [
            { required: true, message: '请选择状态', trigger: 'change' }
          ]
        },
        proxyMethods: [
          { value: 'aes-128-gcm', label: 'aes-128-gcm' },
          { value: 'aes-192-gcm', label: 'aes-192-gcm' },
          { value: 'aes-256-gcm', label: 'aes-256-gcm' },
          { value: 'aes-128-cfb', label: 'aes-128-cfb' },
          { value: 'aes-192-cfb', label: 'aes-192-cfb' },
          { value: 'aes-256-cfb', label: 'aes-256-cfb' },
          { value: 'rc4-md5', label: 'rc4-md5' },
          { value: 'chacha20', label: 'chacha20' }
        ],
        // 图表相关数据
        chartVisible: false,
        chartData: null,
        weightInfoVisible: false,
        chartLoading: false,
        selectedServer: null
      }
    },
    created() {
      // 初始化时加载地区列表
      this.fetchRegionOptions();
      // 初始化加载数据
      this.fetchChanges();
    },
    mounted() {
      // 确保数据加载
      if (!this.changesList.length) {
        this.fetchChanges();
      }
    },
    beforeDestroy() {
      // 清理全局资源
      if (window.myPerformanceChart) {
        window.myPerformanceChart.dispose();
        window.myPerformanceChart = null;
      }
      
      // 移除resize事件监听
      if (window.chartResizeHandler) {
        window.removeEventListener('resize', window.chartResizeHandler);
        window.chartResizeHandler = null;
      }
    },
    watch: {
      // 不再需要监听region_id变化
    },
    methods: {
      // 获取地区选项
      async fetchRegionOptions() {
        try {
          const response = await region_list({});
          if (response.code === 200 && response.data && response.data.lists) {
            // 设置下拉选项
            this.regionOptions = response.data.lists.map(item => ({
              value: item.id,
              label: `${item.regin_code || ''}-${item.cn_name || ''}-${item.regin_name || ''}`
            }));

            // 构建regionMap
            this.regionMap = {};
            response.data.lists.forEach(item => {
              this.regionMap[item.id] = item;
            });
          }
        } catch (error) {
          console.error('获取地区列表失败:', error);
        }
      },
      open(regionId, reginName, cnName) {
        if (regionId) {
          this.region_id = regionId;
          this.searchForm.region_id = regionId;
          this.regin_name = reginName || '';
          this.cn_name = cnName || '';
        }
        this.fetchChanges();
      },
      // 处理页码变化
      handleCurrentChange(val) {
        this.currentPage = val;
        this.fetchChanges();
      },
      // 处理每页数据量变化
      handleSizeChange(val) {
        this.pageSize = val;
        this.currentPage = 1;
        this.fetchChanges();
      },
      // 处理搜索
      handleSearch() {
        this.currentPage = 1;
        this.fetchChanges();
      },
      // 重置搜索表单
      resetSearchForm() {
        this.searchForm = {
          region_id: '',
          status: '',
          ip: ''
        };
        this.currentPage = 1;
        this.fetchChanges();
      },
      // 根据region_id获取完整的地区信息
      getRegionInfo(regionId) {
        return this.regionMap[regionId] || null;
      },

      togglePasswordVisibility(id) {
        this.$set(this.passwordVisible, id, !this.passwordVisible[id])
      },
      async fetchChanges() {
        this.loading = true;
        try {
          const params = {
            page: this.currentPage,
            size: this.pageSize,
            ...this.searchForm
          };

          const res = await region_changes(params);
          if (res.code === 200 && res.data) {
            this.changesList = res.data.lists || [];
            this.total = parseInt(res.data.total || 0);
            this.currentPage = parseInt(res.data.current_page || 1);

            if (this.changesList.length === 0 && this.total > 0) {
              // 如果当前页没有数据但总数不为0，回到第一页
              this.currentPage = 1;
              this.fetchChanges();
            }
          } else {
            this.changesList = [];
            this.total = 0;
          }
        } catch (error) {
          console.error('获取中转服务器列表失败:', error);
          this.changesList = [];
          this.total = 0;
        } finally {
          this.loading = false;
        }
      },
      handleEdit(row) {
        this.editForm = {
          change_id: row.id,
          guid: row.guid,
          region_id: row.region_id,
          ip: row.ip,
          port: row.port,
          password: row.password,
          method: row.method,
          broadband: row.broadband,
          memory: row.memory,
          status: row.status
        }
        this.editDialog = true
      },
      closeEditDialog() {
        this.editDialog = false
        this.resetEditForm()
      },
      resetEditForm() {
        this.editForm = {
          change_id: null,
          guid: '',
          region_id: '',
          ip: '',
          port: '',
          password: '',
          method: '',
          broadband: 0,
          memory: 0,
          status: 1
        }
        if (this.$refs.editForm) {
          this.$refs.editForm.resetFields()
        }
      },
      submitEditForm() {
        this.$refs.editForm.validate(async valid => {
          if (!valid) return

          this.submitLoading = true
          try {
            const res = await region_changes_edit(this.editForm)
            if (res.code === 200) {
              this.closeEditDialog()
              this.fetchChanges() // 刷新列表
            }
          } catch (error) {
            console.error('操作中转服务器失败:', error)
          } finally {
            this.submitLoading = false
          }
        })
      },
      handleDelete(row) {
        this.$confirm('确认删除该中转服务器?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const res = await region_changes_del({ change_id: row.id })
            if (res.code === 200) {
              this.fetchChanges() // 刷新列表
            }
          } catch (error) {
            console.error('删除中转服务器失败:', error)
          }
        }).catch(() => {
          // 用户取消删除
        })
      },
      handleRefreshServerNum() {
        this.fetchChanges();
      },
      // 显示预估权重信息
    showWeightInfo() {
      this.weightInfoVisible = true;
    },
      // 显示服务器性能图表
      async showChangesChart(row) {
        this.chartLoading = true
        this.selectedServer = row
        this.chartVisible = true
        
        // 确保清空之前的数据
        this.chartData = null
        
        // 如果图表实例存在，先销毁它
        if (window.myPerformanceChart) {
          window.myPerformanceChart.dispose()
          window.myPerformanceChart = null
        }
        
        try {
          const response = await region_changes_echarts({ rid: row.id })
          if (response.code === 200) {
            // 验证返回的数据是否有效
            if (response.data && 
                typeof response.data === 'object' && 
                response.data.datasets && 
                response.data.labels) {
              this.chartData = response.data
              this.$nextTick(() => {
                this.initChart()
              })
            } else {
              console.error('获取的图表数据格式不正确:', response.data);
            }
          } else {
            this.$message.error(response.msg || '获取中转服务器性能数据失败')
          }
        } catch (error) {
          console.error('获取中转服务器性能数据失败:', error)
        } finally {
          this.chartLoading = false
        }
      },
      
      // 初始化图表
      initChart() {
        // 更严格的数据验证
        if (!this.chartData || 
            typeof this.chartData !== 'object' || 
            !this.chartData.datasets || 
            !this.chartData.labels ||
            !this.chartData.datasets.traffic ||
            !this.chartData.datasets.memory ||
            !this.chartData.datasets.cpu) {
          console.error('图表数据格式不正确:', this.chartData);
          this.$message.error('图表数据格式不正确，无法显示');
          return;
        }
        
        // 获取DOM元素
        const chartDom = document.getElementById('performanceChart')
        
        // 如果之前有图表实例，先销毁
        if (window.myPerformanceChart) {
          window.myPerformanceChart.dispose()
        }
        
        // 创建新的图表实例
        const myChart = echarts.init(chartDom)
        
        // 保存图表实例到全局变量，方便后续操作
        window.myPerformanceChart = myChart

        // 格式化流量，将字节转换为MB
        const formatTraffic = (bytes) => {
          const mb = bytes / (1024 * 1024)
          return mb.toFixed(2)
        }

        // 将流量数据转换为MB
        const trafficData = this.chartData.datasets.traffic.map(value => formatTraffic(value))

        const option = {
          title: {
            text: '中转服务器性能监控'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            },
            formatter: function(params) {
              let result = params[0].name + '<br/>'

              params.forEach(param => {
                const marker = param.marker
                const seriesName = param.seriesName
                const value = param.value

                if (seriesName === '流量(MB)') {
                  result += `${marker} ${seriesName}: ${value} MB<br/>`
                } else {
                  result += `${marker} ${seriesName}: ${value}%<br/>`
                }
              })

              return result
            }
          },
          legend: {
            data: ['流量(MB)', '内存使用率(%)', 'CPU使用率(%)']
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: this.chartData.labels
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '流量(MB)',
              position: 'left'
            },
            {
              type: 'value',
              name: '使用率(%)',
              position: 'right',
              min: 0,
              max: 100
            }
          ],
          series: [
            {
              name: '流量(MB)',
              type: 'line',
              smooth: true,
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: trafficData
            },
            {
              name: '内存使用率(%)',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              emphasis: {
                focus: 'series'
              },
              data: this.chartData.datasets.memory
            },
            {
              name: 'CPU使用率(%)',
              type: 'line',
              yAxisIndex: 1,
              smooth: true,
              emphasis: {
                focus: 'series'
              },
              data: this.chartData.datasets.cpu
            }
          ]
        }

        myChart.setOption(option)
        
        // 移除可能存在的之前的resize监听器
        window.removeEventListener('resize', window.chartResizeHandler)
        
        // 创建新的resize处理函数并保存引用
        window.chartResizeHandler = () => {
          myChart.resize()
        }
        
        // 添加新的resize监听器
        window.addEventListener('resize', window.chartResizeHandler)
      },
      
      // 在对话框关闭时释放图表资源
      closeChartDialog() {
        this.chartVisible = false
        
        // 延迟执行，确保对话框动画完成后再销毁图表
        setTimeout(() => {
          if (window.myPerformanceChart) {
            window.myPerformanceChart.dispose()
            window.myPerformanceChart = null
          }
          
          // 移除resize事件监听
          if (window.chartResizeHandler) {
            window.removeEventListener('resize', window.chartResizeHandler)
            window.chartResizeHandler = null
          }
        }, 300)
      },
      handleAddRelay() {
        // 先选择地区
        this.selectedRegionId = '';
        this.regionSelectVisible = true;
      },

      // 确认选择地区
      confirmRegionSelect() {
        if (!this.selectedRegionId) {
          this.$message.warning('请选择一个地区');
          return;
        }

        // 关闭地区选择对话框
        this.regionSelectVisible = false;

        // 初始化编辑表单
        this.editForm = {
          change_id: null,
          guid: '',
          region_id: this.selectedRegionId,
          ip: '',
          port: '',
          password: '',
          method: '',
          broadband: 0,
          memory: 0,
          status: 1
        };

        // 打开编辑对话框
        this.editDialog = true;
      },
    }
  }
  </script>

  <style lang="scss" scoped>
  .app-container {
    padding: 20px;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .changes-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    .title-before {
      position: relative;
      padding-left: 10px;
      font-size: 16px;
      font-weight: bold;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background-color: #409EFF;
      }
    }

    .el-card__body {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .search-section {
    margin-bottom: 20px;
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
  }

  .list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .el-table {
      margin-bottom: 20px;
    }
  }

  .pagination-container {
    text-align: right;
  }

  .password-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .password-toggle-btn {
    margin-left: 5px;
    padding: 0;
  }

  .edit-btn {
    color: #409EFF;
  }

  .delete-btn {
    color: #F56C6C;
  }

  .weight-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chart-icon {
    margin-left: 8px;
    color: #409EFF;
    cursor: pointer;
    font-size: 18px;
  }

  .chart-icon:hover {
    color: #66b1ff;
  }

  .chart-container {
    min-height: 400px;
  }

  .server-info {
    margin-bottom: 15px;
  }

  .server-info p {
    margin: 0;
  }

  .chart {
    width: 100%;
    height: 400px;
  }
  </style>
