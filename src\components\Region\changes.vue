<template>
  <div>
    <el-dialog
      :visible.sync="dialog"
      :title="`ID:${region_id}-${cn_name} (${regin_name}) 的中转服务器列表`"
      :append-to-body="true"
      :before-close="close"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="changes-container">
        <div style="margin-bottom: 10px;">
          <el-tag height="30px">预估权重 = 0.42 × (时间窗口流量 / 最大时间窗口流量)
            + 0.25 × (时间窗口连接数 / 最大时间窗口连接数 1000)
            + 0.17 × (CPU使用率)
            + 0.17 × (内存使用率) 
          </el-tag>
        </div>
      
        <el-table
          v-loading="loading"
          :data="changesList"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="IP" align="center" min-width="120" >
            <template slot-scope="scope">
                <div>{{ scope.row.ip }}:{{ scope.row.port}}</div>
                <div>GUID:{{ scope.row.guid }}</div>
            </template>
          </el-table-column>
          <el-table-column label="密码/加密方式" align="center" min-width="160" >
            <template slot-scope="scope">
                <div>PWD:{{ scope.row.password || '' }}</div>
                <div>Method:{{ scope.row.method || '' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="连接数/CPU" align="center" width="120">
            <template slot-scope="scope">
                <div>connect:{{ scope.row.connect_num || 0 }}</div>
                <div>CPU:{{ scope.row.cup || 0.00 }}%</div>
            </template>
          </el-table-column>
          <el-table-column label="带宽/内存(M)" align="center" width="120">
            <template slot-scope="scope">
                <div>{{ scope.row.broadband || 0 }}M / {{ scope.row.use_broadband || 0.00 }}%</div>
                <div>{{ scope.row.memory || 0 }}M / {{ scope.row.use_memory || 0.00 }}%</div>
            </template>
          </el-table-column>
          <el-table-column label="预估权重" align="center" width="150">
            <template slot-scope="scope">
              <div class="weight-container">
                <span>{{ scope.row.weight }}</span>
                <i class="el-icon-s-data chart-icon" @click.stop="showChangesChart(scope.row)" title="查看性能图表"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态/心跳" align="center" width="160">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 
               scope.row.status === 2 ? 'danger' : 
               scope.row.status === 3 ? 'info' : 'warning'">
              {{ scope.row.status === 1 ? '启用' : 
                scope.row.status === 2 ? '禁用' : 
                scope.row.status === 3 ? '离线' : '未知状态' }}
            </el-tag>
              <div>{{ scope.row.last_time }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="edit-btn"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="delete-btn"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 编辑对话框 -->
    <el-dialog
      :visible.sync="editDialog"
      title="编辑中转服务器"
      :append-to-body="true"
      width="500px"
    >
      <el-form ref="editForm" :model="editForm" :rules="rules" label-width="80px">
        <el-form-item label="GUID" prop="guid">
          <el-input v-model="editForm.guid" placeholder="请输入中转服务器的GUID"></el-input>
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input v-model="editForm.ip" placeholder="请输入IP"></el-input>
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input v-model="editForm.port" placeholder="请输入端口号"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="editForm.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="加密方式" prop="method">
          <el-select v-model="editForm.method" placeholder="请选择加密方式">
            <el-option
              v-for="item in proxyMethods"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="editForm.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="带宽(M)" prop="broadband">
          <el-input type="number" v-model="editForm.broadband" placeholder="请输带宽(M)"></el-input>
        </el-form-item>
        <el-form-item label="内存(M)" prop="memory">
          <el-input type="number"  v-model="editForm.memory" placeholder="请输内存(M)"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
            <el-radio :label="3">离线</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeEditDialog">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitEditForm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 性能图表弹窗 -->
    <el-dialog
      title="中转服务器性能监控"
      :visible.sync="chartVisible"
      width="800px"
      center
      :append-to-body="true"
      :before-close="closeChartDialog"
      :close-on-click-modal="false"
    >
      <div v-loading="chartLoading" class="chart-container">
        <div v-if="selectedServer" class="server-info">
          <p><strong>中转服务器:</strong> {{ selectedServer.ip }}:{{ selectedServer.port }} ({{ selectedServer.guid }})</p>
        </div>
        <div id="performanceChart" class="chart"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeChartDialog">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { region_changes, region_changes_edit, region_changes_del, region_changes_echarts } from '@/api/server'
import * as echarts from 'echarts'

export default {
  data() {
    return {
      dialog: false,
      editDialog: false,
      region_id: null,
      regin_name: '',
      cn_name: '',
      changesList: [],
      loading: false,
      submitLoading: false,
      passwordVisible: {},
      editForm: {
        change_id: null,
        guid: '',
        ip: '',
        port: '',
        password: '',
        method: '',
        status: 1,
        broadband: 0,
        memory: 0
      },
      rules: {
        guid: [
          { required: true, message: '请输入GUID', trigger: 'blur' }
        ],
        ip: [
          { required: true, message: '请输入IP', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        method: [
          { required: true, message: '请选择加密方式', trigger: 'change' }
        ],
        broadband: [
          { required: true, message: '请输入带宽(M)', trigger: 'blur' },
          { 
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('带宽必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur' 
          }
        ],
        memory: [
          { required: true, message: '请输入内存(M)', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('内存必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      proxyMethods: [
        { value: 'aes-128-gcm', label: 'aes-128-gcm' },
        { value: 'aes-192-gcm', label: 'aes-192-gcm' },
        { value: 'aes-256-gcm', label: 'aes-256-gcm' },
        { value: 'aes-128-cfb', label: 'aes-128-cfb' },
        { value: 'aes-192-cfb', label: 'aes-192-cfb' },
        { value: 'aes-256-cfb', label: 'aes-256-cfb' },
        { value: 'rc4-md5', label: 'rc4-md5' },
        { value: 'chacha20', label: 'chacha20' }
      ],
      // 图表相关数据
      chartVisible: false,
      chartData: null,
      chartLoading: false,
      selectedServer: null
    }
  },
  beforeDestroy() {
    // 清理全局资源
    if (window.myChangesChart) {
      window.myChangesChart.dispose();
      window.myChangesChart = null;
    }
    
    // 移除resize事件监听
    if (window.changesChartResizeHandler) {
      window.removeEventListener('resize', window.changesChartResizeHandler);
      window.changesChartResizeHandler = null;
    }
  },
  methods: {
    open(regionId, reginName, cnName) {
      this.region_id = regionId
      this.regin_name = reginName || ''
      this.cn_name = cnName || ''
      this.dialog = true
      this.fetchChanges()
    },
    close() {
      this.dialog = false
      this.changesList = []
      this.passwordVisible = {}
      this.regin_name = ''
      this.cn_name = ''
    },
    togglePasswordVisibility(id) {
      this.$set(this.passwordVisible, id, !this.passwordVisible[id])
    },
    async fetchChanges() {
      if (!this.region_id) return
      
      this.loading = true
      try {
        const res = await region_changes({ region_id: this.region_id })
        if (res.code === 200 && res.data && res.data.lists) {
          this.changesList = res.data.lists
        } else {
          this.$message.error(res.msg || '获取中转服务器列表失败')
        }
      } catch (error) {
        console.error('获取中转服务器列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    handleEdit(row) {
      this.editForm = {
        change_id: row.id,
        guid: row.guid,
        region_id: this.region_id,
        ip: row.ip,
        port: row.port,
        password: row.password,
        method: row.method,
        broadband: row.broadband,
        memory: row.memory,
        status: row.status
      }
      this.editDialog = true
    },
    closeEditDialog() {
      this.editDialog = false
      this.resetEditForm()
    },
    resetEditForm() {
      this.editForm = {
        change_id: '',
        guid: '',
        region_id: this.region_id,
        ip: '',
        port: '',
        password: '',
        method: '',
        broadband: 0,
        memory: 0,
        status: 1
      }
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields()
      }
    },
    submitEditForm() {
      this.$refs.editForm.validate(async valid => {
        if (!valid) return
        
        this.submitLoading = true
        try {
          const res = await region_changes_edit(this.editForm)
          if (res.code === 200) {
            this.$message.success('修改成功')
            this.closeEditDialog()
            this.fetchChanges() // 刷新列表
          } else {
            this.$message.error(res.msg || '修改失败')
          }
        } catch (error) {
          console.error('修改中转服务器失败:', error)
        } finally {
          this.submitLoading = false
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该中转服务器?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await region_changes_del({ change_id: row.id })
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.fetchChanges() // 刷新列表
          } else {
            this.$message.error(res.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除中转服务器失败:', error)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 显示服务器性能图表
    async showChangesChart(row) {
      this.chartLoading = true
      this.selectedServer = row
      this.chartVisible = true
      
      // 确保清空之前的数据
      this.chartData = null
      
      // 如果图表实例存在，先销毁它
      if (window.myChangesChart) {
        window.myChangesChart.dispose()
        window.myChangesChart = null
      }
      
      try {
        const response = await region_changes_echarts({ rid: row.id })
        if (response.code === 200) {
          // 验证返回的数据是否有效
          if (response.data && 
              typeof response.data === 'object' && 
              response.data.datasets && 
              response.data.labels) {
            this.chartData = response.data
            this.$nextTick(() => {
              this.initChart()
            })
          } else {
            console.error('获取的图表数据格式不正确:', response.data)
          }
        } else {
          this.$message.error(response.msg || '获取中转服务器性能数据失败')
        }
      } catch (error) {
        console.error('获取中转服务器性能数据失败:', error)
      } finally {
        this.chartLoading = false
      }
    },
    
    // 初始化图表
    initChart() {
      // 更严格的数据验证
      if (!this.chartData || 
          typeof this.chartData !== 'object' || 
          !this.chartData.datasets || 
          !this.chartData.labels ||
          !this.chartData.datasets.traffic ||
          !this.chartData.datasets.memory ||
          !this.chartData.datasets.cpu) {
        console.error('图表数据格式不正确:', this.chartData)
        this.$message.error('图表数据格式不正确，无法显示')
        return
      }
      
      // 获取DOM元素
      const chartDom = document.getElementById('performanceChart')
      
      // 如果之前有图表实例，先销毁
      if (window.myChangesChart) {
        window.myChangesChart.dispose()
      }
      
      // 创建新的图表实例
      const myChart = echarts.init(chartDom)
      
      // 保存图表实例到全局变量，方便后续操作
      window.myChangesChart = myChart
      
      // 格式化流量，将字节转换为MB
      const formatTraffic = (bytes) => {
        const mb = bytes / (1024 * 1024)
        return mb.toFixed(2)
      }
      
      // 将流量数据转换为MB
      const trafficData = this.chartData.datasets.traffic.map(value => formatTraffic(value))
      
      const option = {
        title: {
          text: '中转服务器性能监控'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            
            params.forEach(param => {
              const marker = param.marker
              const seriesName = param.seriesName
              const value = param.value
              
              if (seriesName === '流量(MB)') {
                result += `${marker} ${seriesName}: ${value} MB<br/>`
              } else {
                result += `${marker} ${seriesName}: ${value}%<br/>`
              }
            })
            
            return result
          }
        },
        legend: {
          data: ['流量(MB)', '内存使用率(%)', 'CPU使用率(%)']
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.chartData.labels
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '流量(MB)',
            position: 'left'
          },
          {
            type: 'value',
            name: '使用率(%)',
            position: 'right',
            min: 0,
            max: 100
          }
        ],
        series: [
          {
            name: '流量(MB)',
            type: 'line',
            smooth: true,
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: trafficData
          },
          {
            name: '内存使用率(%)',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.datasets.memory
          },
          {
            name: 'CPU使用率(%)',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.datasets.cpu
          }
        ]
      }
      
      myChart.setOption(option)
      
      // 移除可能存在的之前的resize监听器
      window.removeEventListener('resize', window.changesChartResizeHandler)
      
      // 创建新的resize处理函数并保存引用
      window.changesChartResizeHandler = () => {
        myChart.resize()
      }
      
      // 添加新的resize监听器
      window.addEventListener('resize', window.changesChartResizeHandler)
    },
    
    // 在对话框关闭时释放图表资源
    closeChartDialog() {
      this.chartVisible = false
      
      // 延迟执行，确保对话框动画完成后再销毁图表
      setTimeout(() => {
        if (window.myChangesChart) {
          window.myChangesChart.dispose()
          window.myChangesChart = null
        }
        
        // 移除resize事件监听
        if (window.changesChartResizeHandler) {
          window.removeEventListener('resize', window.changesChartResizeHandler)
          window.changesChartResizeHandler = null
        }
      }, 300)
    }
  }
}
</script>

<style scoped>
.changes-container {
  width: 100%;
}

.password-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle-btn {
  margin-left: 5px;
  padding: 0;
}

.edit-btn {
  color: #409EFF;
}

.delete-btn {
  color: #F56C6C;
}

.weight-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-icon {
  margin-left: 8px;
  color: #409EFF;
  cursor: pointer;
  font-size: 18px;
}

.chart-icon:hover {
  color: #66b1ff;
}

.chart-container {
  min-height: 400px;
}

.server-info {
  margin-bottom: 15px;
}

.server-info p {
  margin: 0;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>
