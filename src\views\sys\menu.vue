<template>
  <div class="app-container">
    <!-- 菜单管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">菜单管理</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddMenu">新增菜单</el-button>
          <el-button type="text" @click="clearCache">清除缓存</el-button>
        </div>
      </div>

      <!-- 菜单列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="menuList"
          row-key="mid"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          :tree-props="{children: 'menu'}"
          style="width: 100%"
        >
          <el-table-column prop="mid" label="菜单ID" width="120" align="center"></el-table-column>
          <el-table-column prop="title" label="标题" min-width="150"></el-table-column>
          <el-table-column label="类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getTypeTagType(scope.row.type)">
                {{ formatType(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="show_action" label="VUE使用URL" min-width="200">
            <template slot-scope="scope">
              <div class="url-content">{{ scope.row.show_action }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="action" label="页面目录URL" min-width="200">
            <template slot-scope="scope">
              <div class="url-content">{{ scope.row.action }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" width="80" align="center"></el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.disable === 1 ? 'success' : 'danger'">
                {{ scope.row.disable === 1 ? '展示' : '不展示' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="280">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEditMenu(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDeleteMenu(scope.row)"
              >删除</el-button>
              <el-button
                v-if="scope.row.type !== 3"
                size="mini"
                type="success"
                @click="handleAddSubMenu(scope.row)"
              >添加子菜单</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>


    <!-- 菜单编辑对话框 -->
    <el-dialog
      :title="menuDialogTitle"
      :visible.sync="menuDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="menuFormRef"
        :model="menuForm"
        :rules="menuFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="menuForm.title" placeholder="请输入菜单标题"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="menuForm.type" placeholder="请选择菜单类型">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父菜单" prop="pid">
          <el-cascader
            v-model="menuForm.pid"
            :options="menuOptions"
            :props="{
              checkStrictly: true,
              value: 'mid',
              label: 'title',
              children: 'children',
              emitPath: false
            }"
            placeholder="请选择父菜单"
            clearable
            show-all-levels
          ></el-cascader>
        </el-form-item>
        <el-form-item label="VUE使用URL" prop="show_action">
          <el-input v-model="menuForm.show_action" placeholder="请输入VUE使用URL"></el-input>
        </el-form-item>
        <el-form-item label="页面目录URL" prop="action">
          <el-input v-model="menuForm.action" placeholder="请输入页面目录URL"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="menuForm.sort" :min="0" placeholder="请输入排序"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="disable">
          <el-radio-group v-model="menuForm.disable">
            <el-radio :label="1">展示</el-radio>
            <el-radio :label="2">不展示</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="menuForm.icon" placeholder="请输入图标"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="menuDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitMenuForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { menu_list, menu_add, menu_edit, menu_del, role_clear } from '@/api/sys'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义表格数据
const menuList = ref([]) // 用于表格显示的处理后数据
const originalMenuList = ref([]) // 原始API数据，用于生成菜单选项
const loading = ref(false)

// 菜单表单对话框
const menuDialogVisible = ref(false)
const menuDialogTitle = ref('')
const menuForm = reactive({
  mid: '',
  title: '',
  type: 1,
  pid: '0', // 默认选择顶级菜单
  action: '',
  show_action: '',
  sort: 0,
  disable: 1,
  icon: ''
})

// 表单验证规则
const menuFormRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  pid: [{ required: true, message: '请选择父菜单', trigger: 'change' }],
  action: [{ required: true, message: '请输入URL', trigger: 'blur' }],
  show_action: [{ required: true, message: '请输入显示URL', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
}

// 菜单表单ref
const menuFormRef = ref(null)

// 菜单选项
const menuOptions = ref([])

// 类型选项
const typeOptions = [
  { label: '目录', value: 1 },
  { label: '菜单', value: 2 },
  { label: '接口', value: 3 }
]
// 处理菜单数据，将 button 数组合并到 menu 中显示
const processMenuData = (menuData) => {
  return menuData.map(item => {
    const processedItem = { ...item }

    // 合并 menu 和 button 数组到 menu 属性中
    const children = []

    // 添加 menu 子菜单
    if (item.menu && item.menu.length > 0) {
      children.push(...processMenuData(item.menu))
    }

    // 添加 button 按钮/接口
    if (item.button && item.button.length > 0) {
      children.push(...processMenuData(item.button))
    }

    // 如果有子项，设置到 menu 属性中（用于表格树形显示）
    if (children.length > 0) {
      processedItem.menu = children
    } else {
      processedItem.menu = []
    }

    return processedItem
  })
}

// 获取菜单列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await menu_list()
    // 保存原始数据用于生成菜单选项
    originalMenuList.value = res.data.lists
    // 处理数据，将 button 合并到显示结构中
    menuList.value = processMenuData(res.data.lists)
    generateMenuOptions()
  } catch (error) {
    console.error('获取菜单列表失败', error)
  } finally {
    loading.value = false
  }
}

// 生成菜单选项
const generateMenuOptions = () => {
  // 添加顶级菜单选项
  const topLevelOption = {
    mid: '0',
    title: '顶级菜单',
    children: []
  }

  // 使用原始数据生成其他菜单选项
  const otherOptions = originalMenuList.value.map(item => {
    let children = []
    if (item.type === 1) {
      // type=1 目录：可能包含 menu 或 button 数组
      children = (item.menu || item.button) ? flattenMenuOptions(item.menu || item.button) : []
    } else if (item.type === 2) {
      // type=2 菜单：只包含 button 数组
      children = item.button ? flattenMenuOptions(item.button) : []
    }

    return {
      mid: item.mid.toString(),
      title: item.title,
      children: children
    }
  })

  // 将顶级菜单选项放在最前面
  menuOptions.value = [topLevelOption, ...otherOptions]

  // 调试信息
  console.log('生成的菜单选项:', menuOptions.value)
}

// 扁平化菜单选项
const flattenMenuOptions = (menu) => {
  return menu.map(item => {
    return {
      mid: item.mid.toString(),
      title: item.title,
      children: (item.menu || item.button) ? flattenMenuOptions(item.menu || item.button) : []
    }
  })
}

// 格式化类型
const formatType = (row) => {
  const types = { 1: '目录', 2: '菜单', 3: '接口' }
  return types[row.type] || '未知'
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  switch (type) {
    case 1:
      return 'primary'
    case 2:
      return 'success'
    case 3:
      return 'warning'
    default:
      return 'info'
  }
}
// 打开添加菜单对话框
const handleAddMenu = () => {
  menuDialogTitle.value = '新增菜单'
  // 重置表单
  Object.keys(menuForm).forEach(key => {
    if (key === 'type') {
      menuForm[key] = 1
    } else if (key === 'pid') {
      menuForm[key] = '0' // 默认选择顶级菜单
    } else if (key === 'sort') {
      menuForm[key] = 0
    } else if (key === 'disable') {
      menuForm[key] = 1
    } else {
      menuForm[key] = ''
    }
  })
  menuDialogVisible.value = true
}

// 打开添加子菜单对话框
const handleAddSubMenu = (parentMenu) => {
  menuDialogTitle.value = '新增子菜单'
  // 重置表单
  Object.keys(menuForm).forEach(key => {
    if (key === 'type') {
      menuForm[key] = parentMenu.type === 1 ? 2 : 3
    } else if (key === 'pid') {
      menuForm[key] = parentMenu.mid.toString()
    } else if (key === 'sort') {
      menuForm[key] = 0
    } else if (key === 'disable') {
      menuForm[key] = 1
    } else {
      menuForm[key] = ''
    }
  })
  menuDialogVisible.value = true
}

// 打开编辑菜单对话框
const handleEditMenu = (row) => {
  menuDialogTitle.value = '编辑菜单'
  // 填充表单数据
  menuForm.mid = row.mid || ''
  menuForm.title = row.title || ''
  menuForm.type = row.type || 1
  // 修复父菜单选择显示问题
  if (row.pid && row.pid !== 0) {
    menuForm.pid = row.pid.toString()
  } else {
    menuForm.pid = '0'
  }
  menuForm.action = row.action || ''
  menuForm.show_action = row.show_action || ''
  menuForm.sort = row.sort || 0
  menuForm.disable = row.disable || 1
  menuForm.icon = row.icon || ''

  menuDialogVisible.value = true
}
// 提交菜单表单
const submitMenuForm = async () => {
  if (!menuFormRef.value) return

  menuFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const params = {
          type: menuForm.type.toString(),
          pid: menuForm.pid,
          title: menuForm.title,
          action: menuForm.action,
          show_action: menuForm.show_action,
          disable: menuForm.disable.toString(),
          icon: menuForm.icon,
          sort: menuForm.sort.toString()
        }

        if (menuForm.mid) {
          params.mid = menuForm.mid
          const res = await menu_edit(params)
          if (res.code === 200) {
            menuDialogVisible.value = false
            proxy.$message.success(res.msg || '编辑菜单成功')
            getList() // 刷新列表
          } else {
            proxy.$message.error(res.msg || '编辑菜单失败')
          }
        } else {
          const res = await menu_add(params)
          if (res.code === 200) {
            menuDialogVisible.value = false
            proxy.$message.success(res.msg || '新增菜单成功')
            getList() // 刷新列表
          } else {
            proxy.$message.error(res.msg || '新增菜单失败')
          }
        }
      } catch (error) {
        console.error('提交菜单失败', error)
      }
    } else {
      return false
    }
  })
}
// 删除菜单
const handleDeleteMenu = async (row) => {
  try {
    await proxy.$confirm('确定要删除该菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await menu_del({ mid: row.mid.toString() })
    if (res.code === 200) {
      proxy.$message.success(res.msg || '删除菜单成功')
      getList() // 刷新列表
    } else {
      proxy.$message.error(res.msg || '删除菜单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单失败', error)
    }
  }
}

// 清除缓存
const clearCache = async () => {
  try {
    const res = await role_clear()
    if (res.code === 200) {
      proxy.$message.success(res.msg || '清除缓存成功')
    } else {
      proxy.$message.error(res.msg || '清除缓存失败')
    }
  } catch (error) {
    console.error('清除缓存失败', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式

.url-content {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  max-width: 200px;
}
</style>
