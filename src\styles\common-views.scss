// 通用视图样式 - 提取自 src/views 文件夹中的共同CSS类

// ==================== 容器布局 ====================

// 应用容器 - 所有页面的根容器
.app-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

// 卡片容器 - 统一的卡片样式
.page-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

// ==================== 标题样式 ====================

// 标题前的装饰条
.title-before {
  position: relative;
  padding-left: 10px;
  font-size: 16px;
  font-weight: bold;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background-color: #409EFF;
  }
}

// ==================== 头部区域 ====================

// 卡片头部布局
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

// 头部按钮组
.header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  
  .el-button {
    padding: 3px 0;
    margin: 0;
  }
}

// ==================== 搜索区域 ====================

// 搜索表单区域
.search-section {
  margin-bottom: 20px;
}

// 搜索表单样式
.search-form {
  display: flex;
  flex-wrap: wrap;
}

// 日期分隔符
.date-separator {
  margin: 0 5px;
}

// ==================== 列表区域 ====================

// 列表区域容器
.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  
  .el-table {
    margin-bottom: 20px;
  }
}

// ==================== 分页区域 ====================

// 分页容器
.pagination-container {
  text-align: right;
}

// ==================== 表单相关 ====================

// 表单提示文字
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1;
  margin-top: 4px;
}

// ==================== 密码显示组件 ====================

// 密码容器
.password-container {
  display: flex;
  align-items: center;
  justify-content: center;

  .password-text {
    margin-right: 5px;
  }

  .password-toggle-btn {
    padding: 2px;
  }
}

// ==================== 清除浮动 ====================

// 清除浮动工具类
.clearfix:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

// ==================== 状态标签 ====================

// 状态标签容器
.status-tag {
  margin-right: 5px;
}

// ==================== 操作按钮 ====================

// 编辑按钮
.edit-btn {
  color: #409EFF;
}

// 删除按钮
.delete-btn {
  color: #F56C6C;
}

// ==================== 响应式工具类 ====================

// 响应式隐藏
@media (max-width: 768px) {
  .mobile-hide {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-hide {
    display: none !important;
  }
}

// ==================== 图表相关 ====================

// 图表容器
.chart-container {
  width: 100%;
  height: 400px;
}

// 性能图表
.performance-chart {
  width: 100%;
  height: 400px;
}

// 服务器信息
.server-info {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

// ==================== JSON显示 ====================

// JSON内容显示
.json-content {
  max-height: 400px;
  overflow: auto;
  background: #f6f6f6;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

// ==================== 权重信息 ====================

// 权重信息容器
.weight-info {
  line-height: 1.6;
  
  p {
    margin: 8px 0;
  }
}

// ==================== IP信息 ====================

// IP信息描述列表
.ip-info {
  .el-descriptions-item__label {
    font-weight: bold;
  }
}
