<template>
  <div class="app-container">
    <!-- 订单管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">订单管理</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户账号">
            <el-input v-model="searchForm.account" placeholder="请输入用户账号" clearable></el-input>
          </el-form-item>
          <el-form-item label="支付状态">
            <el-select v-model="searchForm.pay_status" placeholder="请选择支付状态" clearable>
              <el-option v-for="item in payStatusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="支付方式">
            <el-select v-model="searchForm.pay_type" placeholder="请选择支付方式" clearable>
              <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单配置">
            <el-select v-model="searchForm.sys_order_id" placeholder="请选择订单配置" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option
                v-for="item in orderConfigList"
                :key="item.id"
                :label="`${item.months}个月 (${item.amount/100}元)`"
                :value="item.id.toString()"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 订单列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="orderList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
           height="calc(100vh - 350px)"
        >
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="order_id" label="订单ID" width="180"></el-table-column>
      <el-table-column prop="account" label="账号"></el-table-column>
      <el-table-column prop="pay_amount" label="支付金额">
        <template #default="scope">
          <div>时长：{{ scope.row.months }} 月 - {{ (scope.row.cny_amount / 100).toFixed(2) }} </div>
          金额：{{ (scope.row.pay_amount / 100).toFixed(2) }}({{ scope.row.currency }})
        </template>
      </el-table-column>
      <el-table-column prop="pay_type" label="支付方式">
        <template #default="scope">
          <el-tag v-if="scope.row.pay_type === 1" type="success">Stripe</el-tag>
          <el-tag v-else-if="scope.row.pay_type === 2" type="warning">Apple</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="pay_status" label="支付状态">
        <template #default="scope">
          <el-tag v-if="scope.row.pay_status === 1" type="info">未支付</el-tag>
          <el-tag v-else-if="scope.row.pay_status === 2" type="success">已支付</el-tag>
          <el-tag v-else-if="scope.row.pay_status === 3" type="danger">过期未支付</el-tag>
          <el-tag v-else-if="scope.row.pay_status === 9" type="danger">创建失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付信息" width="280">
        <template #default="scope">
          <div class="pay-info">
            <div>支付过期时间：{{ scope.row.pay_end_time }}</div>
            <div v-if="scope.row.pay_url" class="pay-url">
              <el-tooltip
                :content="scope.row.pay_url"
                placement="top"
                :show-after="500"
              >
                <span>支付链接：{{ scope.row.pay_url.substring(0, 20) }}...</span>
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="pay_time" label="支付时间"></el-table-column>
      <el-table-column prop="create_time" label="创建时间"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { member_orders, member_order_conf } from '@/api/member'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  account: '',
  pay_status: '',
  pay_type: '',
  sys_order_id: '',
  ct_start: '',
  ct_end: ''
})

// 定义表格数据
const orderList = ref([])
const orderConfigList = ref([])
const loading = ref(false)
const dateRange = ref([])

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 支付状态选项
const payStatusOptions = [
  { label: '全部', value: '' },
  { label: '未支付', value: '1' },
  { label: '已支付', value: '2' },
  { label: '过期未支付', value: '3' },
  { label: '创建失败', value: '9' }
]

// 支付方式选项
const payTypeOptions = [
  { label: '全部', value: '' },
  { label: 'Stripe', value: '1' },
  { label: 'Apple', value: '2' }
]

// 获取订单配置列表
const getOrderConfig = async () => {
  try {
    const res = await member_order_conf()
    orderConfigList.value = res.data.lists
  } catch (error) {
    console.error('获取订单配置失败', error)
  }
}

// 处理日期范围变更
const handleDateRangeChange = (val) => {
  if (val) {
    searchForm.ct_start = val[0]
    searchForm.ct_end = val[1]
  } else {
    searchForm.ct_start = ''
    searchForm.ct_end = ''
  }
}

// 获取订单列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await member_orders(params)
    orderList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取订单列表失败', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  dateRange.value = []
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 处理支付
const handlePay = (row) => {
  if (row.pay_url) {
    window.open(row.pay_url, '_blank')
  }
}

// 页面加载时获取数据
onMounted(() => {
  getOrderConfig()
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式

.pay-info {
  .pay-url {
    margin-top: 5px;
    color: #409EFF;
    cursor: pointer;
  }
}
</style>
