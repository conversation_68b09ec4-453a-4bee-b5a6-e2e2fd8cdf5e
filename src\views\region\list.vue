<template>
  <div class="app-container">
    <!-- 地区列表卡片 -->
    <el-card shadow="hover" class="region-card">
      <div slot="header" class="card-header">
        <span class="title-before">地区列表</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleRefreshServerNum">
            刷新中转/服务器数量
          </el-button>
          <el-button type="text" @click="handleAddRelay">添加中转服务器</el-button>
          <el-button type="text" @click="handleAdd">添加地区</el-button>
        </div>
      </div>

      <!-- 地区列表 -->
      <div class="list-section">
        <el-table v-loading="listLoading" :data="regions" element-loading-text="加载中..." border fit highlight-current-row
          style="width: 100%" height="calc(100vh - 350px)">
          <el-table-column prop="id" label="ID" align="center" width="80" />
          <el-table-column label="国家代码" align="center">
            <template slot-scope="scope">
              <div>
                <world-icon :icon="`word${scope.row.region_icon}`" :size="30" /> {{ scope.row.regin_code }}
              </div>
              <!-- <div>{{ scope.row.regin_code }}</div> -->
            </template>
          </el-table-column>
          <el-table-column label="名称" align="left" width="200">
            <template slot-scope="scope">
              <div>cn: {{ scope.row.cn_name }}</div>
              <div>en: {{ scope.row.regin_name }}</div>
            </template>
          </el-table-column>
          <el-table-column label="中转数量" align="center" >
            <template slot-scope="scope">
              <template v-if="scope.row.change_num && scope.row.change_num > 0">
                <el-button type="text" @click="handleShowChanges(scope.row)">
                  {{ scope.row.change_num }}
                </el-button>
              </template>
              <template v-else>
                {{ scope.row.change_num || 0 }}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="server_num" label="服务器数量" align="center"  />
          <el-table-column prop="region_level" label="地区等级" align="center"  />
          <el-table-column label="属性" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.cn_attributes">
                <el-tag v-for="(tag, index) in scope.row.cn_attributes.split(',')" :key="index" size="mini"
                  style="margin-right: 5px; margin-bottom: 2px;">
                  {{ tag.trim() }}
                </el-tag>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="英文属性" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.en_attributes">
                <el-tag v-for="(tag, index) in scope.row.en_attributes.split(',')" :key="index" size="mini"
                  type="success" style="margin-right: 5px; margin-bottom: 2px;">
                  {{ tag.trim() }}
                </el-tag>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center">
            <template slot-scope="scope">
              {{ formatTime(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑
              </el-button>
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogStatus === 'create' ? '新增地区' : '编辑地区'" :visible.sync="dialogVisible" width="500px"
      :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" status-icon>
        <el-form-item label="选择国家" prop="country_id">
          <el-select v-model="form.country_id" filterable placeholder="请选择国家" @change="handleCountryChange">
            <el-option v-for="item in countryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="国家代码" prop="regin_code">
          <el-input v-model="form.regin_code" placeholder="请输入国家代码，如UK、US等" :disabled="!!form.country_id"></el-input>
        </el-form-item>
        <el-form-item label="英文名称" prop="regin_name">
          <el-input v-model="form.regin_name" placeholder="请输入地区英文名称"></el-input>
        </el-form-item>
        <el-form-item label="中文名称" prop="cn_name">
          <el-input v-model="form.cn_name" placeholder="请输入地区中文名称"></el-input>
        </el-form-item>
        <el-form-item label="地区图标" prop="region_icon">
          <el-input v-model="form.region_icon" placeholder="请输入地区图标类名" :disabled="!!form.country_id"></el-input>
        </el-form-item>
        <el-form-item label="地区等级" prop="region_level">
          <el-input-number v-model="form.region_level" :min="1" :max="10" placeholder="请输入地区等级"
            style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="中文属性" prop="cn_attributes">
          <div class="attributes-container">
            <!-- 预设标签按钮 -->
            <div class="preset-tags">
              <el-button v-for="tag in cnPresetTags" :key="tag" size="mini" type="primary" plain
                @click="addPresetTag(tag, 'cn')" style="margin-right: 8px; margin-bottom: 8px;">
                {{ tag }}
              </el-button>
            </div>

            <!-- 标签输入框容器 -->
            <div class="tag-input-wrapper" @click="focusInput('cn')">
              <!-- 已选择的标签 -->
              <el-tag v-for="(tag, index) in cnSelectedTags" :key="index" closable @close="removeTag(index, 'cn')"
                size="small" class="tag-item">
                {{ tag }}
              </el-tag>

              <!-- 手动输入框 -->
              <input ref="cnTagInput" v-model="cnTagInput" class="tag-input"
                :placeholder="cnSelectedTags.length === 0 ? '输入中文标签按回车添加，或点击上方预设标签' : '继续输入...'"
                @keyup.enter="addCustomTag('cn')" @blur="handleInputBlur('cn')" />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="英文属性" prop="en_attributes">
          <div class="attributes-container">
            <!-- 预设标签按钮 -->
            <div class="preset-tags">
              <el-button v-for="tag in enPresetTags" :key="tag" size="mini" type="success" plain
                @click="addPresetTag(tag, 'en')" style="margin-right: 8px; margin-bottom: 8px;">
                {{ tag }}
              </el-button>
            </div>

            <!-- 标签输入框容器 -->
            <div class="tag-input-wrapper" @click="focusInput('en')">
              <!-- 已选择的标签 -->
              <el-tag v-for="(tag, index) in enSelectedTags" :key="index" closable @close="removeTag(index, 'en')"
                size="small" type="success" class="tag-item">
                {{ tag }}
              </el-tag>

              <!-- 手动输入框 -->
              <input ref="enTagInput" v-model="enTagInput" class="tag-input"
                :placeholder="enSelectedTags.length === 0 ? '输入英文标签按回车添加，或点击上方预设标签' : '继续输入...'"
                @keyup.enter="addCustomTag('en')" @blur="handleInputBlur('en')" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <changeedit ref="changeedit" @success="handleRelaySuccess" />
    <changes ref="changes" @success="handleRelaySuccess" />
  </div>
</template>

<script>
import { region_list, region_edit, region_del, region_clear } from '@/api/server';
import { home_country } from "@/api/sys";
import changeedit from '@/components/Region/changeedit.vue'
import changes from '@/components/Region/changes.vue'

export default {
  components: { changeedit, changes },
  data() {
    return {
      regions: [],
      listLoading: false,
      dialogVisible: false,
      dialogStatus: 'create', // create或edit
      submitLoading: false,
      countryOptions: [],
      countryMap: {}, // 存储国家信息的映射
      cnPresetTags: ['直连', '回国', '加速', 'FQ'], // 中文预设标签
      enPresetTags: ['Direct', 'Return', 'Fast', 'Proxy'], // 英文预设标签
      cnSelectedTags: [], // 已选择的中文标签
      enSelectedTags: [], // 已选择的英文标签
      cnTagInput: '', // 手动输入的中文标签
      enTagInput: '', // 手动输入的英文标签
      form: {
        region_id: 0,
        country_id: null,
        regin_code: '',
        regin_name: '',
        cn_name: '',
        region_icon: '',
        region_level: 1,
        cn_attributes: '',
        en_attributes: '',
        status: 1
      },
      rules: {
        country_id: [
          { required: true, message: '请选择国家', trigger: 'change' }
        ],
        regin_code: [
          { required: true, message: '请输入国家代码', trigger: 'blur' }
        ],
        regin_name: [
          { required: true, message: '请输入地区英文名称', trigger: 'blur' }
        ],
        cn_name: [
          { required: true, message: '请输入地区中文名称', trigger: 'blur' }
        ],
        region_icon: [
          { required: true, message: '请输入地区图标', trigger: 'blur' }
        ],
        region_level: [
          { required: true, message: '请输入地区等级', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
    };
  },
  created() {
    this.fetchRegions();
    this.fetchCountryList();
  },
  methods: {
    async fetchRegions() {
      this.listLoading = true;
      try {
        const response = await region_list({});
        if (response.code === 200) {
          this.regions = response.data.lists;
        }
      } catch (error) {
        console.error('Error fetching regions:', error);
      } finally {
        this.listLoading = false;
      }
    },
    formatTime(timestamp) {
      if (!timestamp || timestamp === 0) return '暂无';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString();
    },
    async fetchCountryList() {
      try {
        const response = await home_country();
        if (response.code === 200) {
          // 保存国家数据到映射对象中
          this.countryMap = {};
          response.data.lists.forEach(item => {
            this.countryMap[item.id] = item;
          });

          this.countryOptions = response.data.lists.map(item => ({
            value: item.id,
            label: item.chinese_name
          }));
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleCountryChange(countryId) {
      if (countryId && this.countryMap[countryId]) {
        const country = this.countryMap[countryId];
        this.form.regin_code = country.countryCode;
        this.form.regin_name = country.full_name;
        this.form.cn_name = country.chinese_name;
        this.form.region_icon = country.icon;
      }
    },
    resetForm() {
      this.form = {
        region_id: 0,
        country_id: null,
        regin_code: '',
        regin_name: '',
        cn_name: '',
        region_icon: '',
        region_level: 1,
        cn_attributes: '',
        en_attributes: '',
        status: 1
      };
      this.cnSelectedTags = [];
      this.enSelectedTags = [];
      this.cnTagInput = '';
      this.enTagInput = '';
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    handleAdd() {
      this.dialogStatus = 'create';
      this.resetForm();
      this.dialogVisible = true;
    },
    handleEdit(row) {
      console.log(row)
      this.dialogStatus = 'edit';
      this.resetForm();
      this.form = {
        region_id: row.id,
        country_id: null, // 编辑时默认不选中任何国家
        regin_code: row.regin_code,
        regin_name: row.regin_name,
        cn_name: row.cn_name,
        region_icon: row.region_icon,
        region_level: row.region_level || 1,
        cn_attributes: row.cn_attributes || '',
        en_attributes: row.en_attributes || '',
        status: row.status
      };

      // 处理中文属性标签
      if (row.cn_attributes) {
        this.cnSelectedTags = row.cn_attributes.split(',').map(tag => tag.trim()).filter(tag => tag);
      }

      // 处理英文属性标签
      if (row.en_attributes) {
        this.enSelectedTags = row.en_attributes.split(',').map(tag => tag.trim()).filter(tag => tag);
      }

      // 尝试找到匹配的国家
      for (const id in this.countryMap) {
        const country = this.countryMap[id];
        if (country.countryCode === row.regin_code) {
          this.form.country_id = parseInt(id);
          break;
        }
      }

      this.dialogVisible = true;
    },
    async submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) {
          return false;
        }

        this.submitLoading = true;
        try {
          const params = {
            region_id: this.form.region_id || 0,
            country_id: this.form.country_id,
            regin_code: this.form.regin_code,
            regin_name: this.form.regin_name,
            cn_name: this.form.cn_name,
            region_icon: this.form.region_icon,
            region_level: this.form.region_level,
            cn_attributes: this.cnSelectedTags.join(','),
            en_attributes: this.enSelectedTags.join(','),
            status: this.form.status
          };

          const response = await region_edit(params);
          if (response.code === 200) {
            this.dialogVisible = false;
            this.fetchRegions(); // 重新获取列表
          }
        } catch (error) {
          console.error('Error submitting form:', error);
        } finally {
          this.submitLoading = false;
        }
      });
    },
    // 删除地区
    async handleDelete(row) {
      this.$confirm('确认删除[' + row.cn_name + ']该地区?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await region_del({ region_id: row.id });
          if (response.code === 200) {
            this.fetchRegions(); // 重新获取列表
          }
        } catch (error) {
          console.error('Error deleting region:', error);
        }
      }).catch(() => {
        // 用户取消删除
      });
    },
    // 刷新地区服务器数量
    async handleRefreshServerNum() {
      try {
        const response = await region_clear();
        if (response.code === 200) {
          this.fetchRegions(); // 重新获取列表
        }
      } catch (error) {
        console.error('Error refreshing server numbers:', error);
      }
    },
    handleAddRelay() {
      this.$refs.changeedit.open()
    },
    handleRelaySuccess() {
      this.fetchRegions() // 直接调用fetchRegions而不是getList
    },
    handleShowChanges(row) {
      this.$refs.changes.open(row.id, row.regin_name, row.cn_name)
    },
    // 添加预设标签
    addPresetTag(tag, type) {
      if (type === 'cn') {
        if (!this.cnSelectedTags.includes(tag)) {
          this.cnSelectedTags.push(tag);
        }
      } else if (type === 'en') {
        if (!this.enSelectedTags.includes(tag)) {
          this.enSelectedTags.push(tag);
        }
      }
    },
    // 添加自定义标签
    addCustomTag(type) {
      if (type === 'cn') {
        const tag = this.cnTagInput.trim();
        if (tag && !this.cnSelectedTags.includes(tag)) {
          this.cnSelectedTags.push(tag);
          this.cnTagInput = '';
        }
      } else if (type === 'en') {
        const tag = this.enTagInput.trim();
        if (tag && !this.enSelectedTags.includes(tag)) {
          this.enSelectedTags.push(tag);
          this.enTagInput = '';
        }
      }
    },
    // 移除标签
    removeTag(index, type) {
      if (type === 'cn') {
        this.cnSelectedTags.splice(index, 1);
      } else if (type === 'en') {
        this.enSelectedTags.splice(index, 1);
      }
    },
    // 聚焦输入框
    focusInput(type) {
      this.$nextTick(() => {
        if (type === 'cn' && this.$refs.cnTagInput) {
          this.$refs.cnTagInput.focus();
        } else if (type === 'en' && this.$refs.enTagInput) {
          this.$refs.enTagInput.focus();
        }
      });
    },
    // 输入框失焦处理
    handleInputBlur(type) {
      // 如果输入框有内容，自动添加为标签
      if (type === 'cn' && this.cnTagInput.trim()) {
        this.addCustomTag('cn');
      } else if (type === 'en' && this.enTagInput.trim()) {
        this.addCustomTag('en');
      }
    }
  }
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.region-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  .title-before {
    position: relative;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background-color: #409EFF;
    }
  }

  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .el-table {
    margin-bottom: 20px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;

  .el-button {
    padding: 3px 0;
    margin: 0;
  }
}

.attributes-container {
  .preset-tags {
    margin-bottom: 12px;
  }

  .tag-input-wrapper {
    min-height: 40px;
    padding: 4px 8px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    cursor: text;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 4px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &:hover {
      border-color: #c0c4cc;
    }

    &:focus-within {
      border-color: #409eff;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    .tag-item {
      margin: 2px 0;
      flex-shrink: 0;
    }

    .tag-input {
      border: none;
      outline: none;
      background: transparent;
      flex: 1;
      min-width: 120px;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
      color: #606266;

      &::placeholder {
        color: #c0c4cc;
      }
    }
  }
}
</style>
