<template>
  <div class="app-container">
    <!-- 服务器心跳卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">服务器心跳</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="地区">
            <el-select v-model="searchForm.country_code" placeholder="请选择地区" clearable>
              <el-option
                v-for="item in regionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="IP地址">
            <el-input v-model="searchForm.nat_ip" placeholder="请输入IP地址" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 服务器心跳列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="beatsList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
          <el-table-column prop="guid" label="GUID" align="center"></el-table-column>
          <el-table-column prop="region_code" label="地区" align="center"></el-table-column>
          <el-table-column prop="nat_ip" label="IP地址" align="center"></el-table-column>
          <el-table-column prop="total_up" label="上行" align="center"></el-table-column>
          <el-table-column prop="total_down" label="下行" align="center"></el-table-column>
          <el-table-column prop="use_memory" label="内存" align="center"></el-table-column>
          <el-table-column prop="use_cpu" label="CPU" align="center"></el-table-column>
          <el-table-column prop="last_time" label="最后心跳时间" align="center"></el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          ></el-pagination>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { server_beats, region_list } from '@/api/server'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义搜索表单数据
const searchForm = reactive({
  nat_ip: '',
  country_code: ''
})

// 定义表格数据
const beatsList = ref([])
const regionOptions = ref([])
const regionMap = ref({}) // 存储地区信息映射
const loading = ref(false)

// 定义分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 获取地区选项
const getRegionOptions = async () => {
  try {
    const res = await region_list({})
    if (res.code === 200) {
      // 清空并重新构建regionMap
      regionMap.value = {}

      // 保存完整的地区数据到映射对象
      res.data.lists.forEach(item => {
        regionMap.value[item.id] = item
      })

      // 设置下拉选项
      regionOptions.value = res.data.lists.map(item => ({
        value: item.regin_code,
        label: `${item.regin_code}-${item.cn_name}-${item.regin_name}`
      }))
    }
  } catch (error) {
    console.error('获取地区选项失败:', error)
  }
}

// 获取服务器心跳列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await server_beats(params)
    beatsList.value = res.data.lists
    pagination.total = res.data.total
    pagination.totalPage = res.data.total_page
  } catch (error) {
    console.error('获取服务器心跳数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索表单
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 页码变更
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 每页条数变更
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 页面加载时获取数据
onMounted(() => {
  getRegionOptions()
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
