<template>
  <div class="home-container">
    <!-- 首页数据预览部分 -->
    <div class="section-container">
      <div v-loading="!homeData" class="card-grid">
        <el-card class="data-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-user"></i>
            <span>用户数据</span>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-label">日活用户:</span>
              <span class="data-value">{{ homeData ? homeData.daily_active_users : '-' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">今日注册:</span>
              <span class="data-value">{{ homeData ? homeData.daily_new_users : '-' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">总用户数:</span>
              <span class="data-value">{{ homeData ? homeData.total_users : '-' }}</span>
            </div>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-shopping-cart-full"></i>
            <span>订单数据</span>
          </div>
          <div class="card-content">
            <div class="data-row">
              <span class="data-label">今日订单/金额:</span>
              <span class="data-value">{{ homeData ? homeData.daily_orders : '0' }}/¥{{ homeData ? homeData.daily_orders_amount : '0' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">今日已付/金额:</span>
              <span class="data-value">{{ homeData ? homeData.daily_paid_orders : '0' }}/¥{{ homeData ? homeData.daily_paid_orders_amount : '0' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">总已付订单数量/金额:</span>
              <span class="data-value">{{ homeData ? homeData.total_paid_orders : '0' }}/¥{{ homeData ? homeData.total_paid_amount : '0' }}</span>
            </div>
          </div>
        </el-card>
        
        <el-card class="data-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-connection"></i>
            <span>代理数据</span>
          </div>
          <div class="card-content">

            <div class="data-row">
              <span class="data-label">中转在线/总数:</span>
              <span class="data-value">{{ homeData ? homeData.total_changes_online : '0' }}/{{ homeData ? homeData.total_changes : '0' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">服务器在线:</span>
              <span class="data-value">{{ homeData ? homeData.total_servers_online : '0-' }}</span>
            </div>
            <div class="data-row">
              <span class="data-label">服务器数量:</span>
              <span class="data-value">{{ homeData ? homeData.total_servers : '0' }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 聊天数据图表部分 -->
    <div class="section-container">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-select v-model="chatDataType" placeholder="请选择" @change="fetchChatData">
            <el-option label="半月" value="1"></el-option>
            <el-option label="一月内" value="2"></el-option>
            <el-option label="三个月" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <div v-loading="chartLoading" class="chart-container">
        <div ref="chatChart" style="width: 100%; height: 400px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { home_data, home_echarts } from '@/api/member'
import * as echarts from 'echarts'

export default {
  data() {
    return {
      homeData: null,
      chatData: null,
      chatDataType: '1',
      chart: null,
      chartLoading: false
    }
  },
  mounted() {
    this.fetchHomeData()
    this.fetchChatData()
  },
  methods: {
    async fetchHomeData() {
      try {
        const response = await home_data()
        if (response.code === 200) {
          this.homeData = response.data
        } else {
          this.$message.error(response.msg || '获取首页数据失败')
        }
      } catch (error) {
        console.error('获取首页数据出错:', error)
      }
    },
    async fetchChatData() {
      this.chartLoading = true
      try {
        const response = await home_echarts({ type: this.chatDataType })
        if (response.code === 200) {
          this.chatData = response.data
          this.$nextTick(() => {
            this.renderChart()
          })
        } else {
          this.$message.error(response.msg || '获取聊天数据失败')
        }
      } catch (error) {
        console.error('获取聊天数据出错:', error)
      } finally {
        this.chartLoading = false
      }
    },
    renderChart() {
      if (!this.chatData) return

      // 使用新的API返回格式
      const { labels, datasets } = this.chatData
      const { daily_active_users, daily_new_users, total_paid_orders, total_paid_amount } = datasets
      
      // 将分转换为元
      const formattedAmounts = total_paid_amount.map(amount => (amount / 100).toFixed(2));

      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chatChart)
      }

      const option = {
        title: { text: '聊天数据统计' },
        tooltip: { 
          trigger: 'axis',
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(param => {
              // 订单金额单独格式化
              if (param.seriesName === '订单金额(元)') {
                result += param.seriesName + ': ' + param.value + ' 元<br/>';
              } else {
                result += param.seriesName + ': ' + param.value + '<br/>';
              }
            });
            return result;
          }
        },
        legend: {
          data: ['日活用户', '新增用户', '已付订单', '订单金额(元)']
        },
        xAxis: { type: 'category', data: labels },
        yAxis: [
          { 
            type: 'value',
            name: '数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '金额(元)',
            position: 'right',
            axisLabel: {
              formatter: '{value} 元'
            }
          }
        ],
        series: [
          { name: '日活用户', type: 'line', data: daily_active_users },
          { name: '新增用户', type: 'line', data: daily_new_users },
          { name: '已付订单', type: 'line', data: total_paid_orders },
          { 
            name: '订单金额(元)', 
            type: 'line', 
            yAxisIndex: 1,
            data: formattedAmounts
          }
        ]
      }

      this.chart.setOption(option)
      
      // 窗口大小变化时重绘图表
      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    
    // 移除resize事件监听
    window.removeEventListener('resize', this.chart?.resize)
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 20px;
}

.section-container {
  margin-bottom: 30px;
}

.section-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.data-card {
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    font-size: 18px;
    color: #409EFF;
  }
  
  span {
    font-weight: bold;
    font-size: 16px;
  }
}

.card-content {
  padding: 10px 0;
}

.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ebeef5;
  
  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
}

.data-label {
  color: #606266;
}

.data-value {
  font-weight: bold;
  color: #303133;
}

.chart-container {
  margin-top: 20px;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

p {
  margin: 8px 0;
}
</style>