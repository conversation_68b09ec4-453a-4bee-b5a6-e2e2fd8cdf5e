<template>
    <div v-if="!item.hidden">
        <template
            v-if="!hasOneShowingChild(item.children,item) && (onlyOneChild.children || !onlyOneChild.noShowingChildren)">
            <el-submenu :index="item.path">
                <template slot="title">
                    <i v-if="item.meta" :class="item.meta.icon" style="font-size: 20px; font-weight: bold"></i>
                    <span slot="title">{{ item.meta.title }}</span>
                </template>
                <menu-item v-for="child in item.children" :key="child.path" :is-nest="true" :item="child"></menu-item>
            </el-submenu>
        </template>

        <template v-else>
            <el-menu-item :index="item.path" @click="$router.push(item.path)">
                <i v-if="item.meta" :class="item.meta.icon" style="font-size: 20px; font-weight: bold"></i>
                <span slot="title">{{ item.meta.title }}</span>
            </el-menu-item>
        </template>
    </div>
</template>

<script>
export default {
    name: 'menuItem',
    props: ['item'],
    data() {
        this.onlyOneChild = null
        return {}
    },
    methods: {
        hasOneShowingChild(children = [], parent) {
            const showingChildren = children.filter(item => {
                if (item.hidden) {
                    return false
                } else {
                    // Temp set(will be used if only has one showing child)
                    this.onlyOneChild = item
                    return true
                }
            })

            // When there is only one child router, the child router is displayed by default
            if (showingChildren.length === 1) {
                return true
            }

            // Show parent if there are no child router to display
            if (showingChildren.length === 0) {
                this.onlyOneChild = {...parent, path: '', noShowingChildren: true}
                return true
            }

            return false
        },
    }
}
</script>

<style>

</style>
