<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { channel_rebate, channel_slist } from '@/api/channel'

// 定义搜索表单数据
const searchForm = reactive({
    channel_id: '',
    maccount: '',
    product_id: '',
    pay_type: '',
    status: '',
    withdrew_status: '',
    ct_start: '',
    ct_end: ''
})

// 分页数据
const pagination = reactive({
    page: 1,
    size: 20,
    total: 0,
    totalPage: 0
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 渠道选项
const channelOptions = ref([])

// 商品选项
const productOptions = [
    { label: '全部', value: '' },
    { label: 'Month30', value: 'SurfelvesSubscribeMonth30' },
    { label: 'Month90', value: 'SurfelvesSubscribeMonth90' },
    { label: 'Month365', value: 'SurfelvesSubscribeMonth365' }
]

// 订单类型选项
const payTypeOptions = [
    { label: '全部', value: '' },
    { label: 'Stripe', value: '1' },
    { label: 'Apple', value: '2' }
]

// 返利类型选项
const statusOptions = [
    { label: '全部', value: '' },
    { label: '购买返利', value: '1' },
    { label: '苹果退款扣返利', value: '5' }
]

// 提现状态选项
const withdrewStatusOptions = [
    { label: '全部', value: '' },
    { label: '未提现', value: '1' },
  { label: '审核中', value: '2' },
    { label: '已经提现', value: '3' }
]

// 获取渠道列表
const getChannelList = async () => {
    try {
        const res = await channel_slist({})
        if (res.code === 200) {
            channelOptions.value = [
                { label: '全部', value: '' },
                ...res.data.map(item => ({
                    label: item.account,
                    value: item.id
                }))
            ]
        }
    } catch (error) {
        console.error('获取渠道列表失败', error)
    }
}

// 获取返利列表数据
const getList = async () => {
    loading.value = true
    try {
        const params = {
            page: pagination.page,
            size: pagination.size,
            ...searchForm
        }
        const res = await channel_rebate(params)
        if (res.code === 200) {
            tableData.value = res.data.lists
            pagination.total = res.data.total
            pagination.totalPage = res.data.total_page
        }
    } catch (error) {
        console.error('获取返利列表失败', error)
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.page = 1
    getList()
}

// 重置搜索
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
    })
    pagination.page = 1
    getList()
}

// 分页大小改变
const handleSizeChange = (val) => {
    pagination.size = val
    pagination.page = 1
    getList()
}

// 当前页改变
const handleCurrentChange = (val) => {
    pagination.page = val
    getList()
}

// 格式化金额（分转元）
const formatAmount = (amount) => {
    if (!amount || amount === 0) return '0.00'
    const yuan = parseFloat(amount) / 100
    return yuan.toFixed(2)
}

// 获取金额颜色
const getAmountColor = (amount) => {
    const yuan = parseFloat(amount) / 100 || 0
    if (yuan >= 100) {
        return '#67C23A' // 绿色 - 高金额 (>=100元)
    } else if (yuan >= 50) {
        return '#E6A23C' // 橙色 - 中等金额 (50-99元)
    } else if (yuan > 0) {
        return '#409EFF' // 蓝色 - 低金额 (>0元)
    } else {
        return '#909399' // 灰色 - 无金额 (0元)
    }
}

// 格式化商品名称
const formatProductName = (productId) => {
    if (!productId) return '-'
    if (productId.startsWith('SurfelvesSubscribe')) {
        return productId.substring(18)
    }
    return productId
}

// 格式化订单类型
const formatPayType = (payType) => {
    switch (payType) {
        case 1:
            return 'Stripe'
        case 2:
            return 'Apple'
        default:
            return '未知'
    }
}

// 格式化返利类型
const formatStatus = (status) => {
    switch (status) {
        case 1:
            return '购买返利'
        case 5:
            return '退款扣返利'
        default:
            return '未知'
    }
}

// 获取返利类型颜色
const getStatusColor = (status) => {
    switch (status) {
        case 1:
            return '#67C23A' // 绿色 - 购买返利
        case 5:
            return '#F56C6C' // 红色 - 退款扣返利
        default:
            return '#909399' // 灰色 - 未知
    }
}

// 格式化提现状态
const formatWithdrewStatus = (status) => {
    switch (status) {
        case 1:
            return '未提现'
        case 2:
            return '审核中'
        case 3:
            return '已经提现'
        default:
            return '未知'
    }
}

// 获取提现状态颜色
const getWithdrewStatusColor = (status) => {
    switch (status) {
        case 1:
            return '#E6A23C' // 橙色 - 未提现
        case 2:
            return '#409EFF' // 蓝色 - 审核中
        case 3:
            return '#67C23A' // 绿色 - 已经提现
        default:
            return '#909399' // 灰色 - 未知
    }
}

// 页面加载时获取数据
onMounted(() => {
    getChannelList()
    getList()
})
</script>

<template>
    <div class="app-container">
        <el-card class="box-card">
            <!-- 页面标题 -->
            <div slot="header" class="clearfix">
                <span class="card-title">推广返利管理</span>
            </div>

            <!-- 搜索表单 -->
            <div class="search-section">
                <el-form :inline="true" :model="searchForm" class="search-form">
                    <el-form-item label="渠道">
                        <el-select v-model="searchForm.channel_id" placeholder="请选择渠道" clearable>
                            <el-option v-for="item in channelOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="用户账号">
                        <el-input v-model="searchForm.maccount" placeholder="请输入用户账号" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="商品">
                        <el-select v-model="searchForm.product_id" placeholder="请选择商品" clearable>
                            <el-option v-for="item in productOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="订单类型">
                        <el-select v-model="searchForm.pay_type" placeholder="请选择订单类型" clearable>
                            <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="返利类型">
                        <el-select v-model="searchForm.status" placeholder="请选择返利类型" clearable>
                            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="提现状态">
                        <el-select v-model="searchForm.withdrew_status" placeholder="请选择提现状态" clearable>
                            <el-option v-for="item in withdrewStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="登录时间">
                        <el-date-picker v-model="searchForm.ct_start" type="date" placeholder="开始日期"
                            value-format="yyyy-MM-dd" style="width: 160px;"></el-date-picker>
                        <span class="date-separator">至</span>
                        <el-date-picker v-model="searchForm.ct_end" type="date" placeholder="结束日期"
                            value-format="yyyy-MM-dd" style="width: 160px;"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 返利列表 -->
            <div class="list-section">
                <el-table v-loading="loading" :data="tableData" element-loading-text="加载中..." border fit
                    highlight-current-row style="width: 100%" height="calc(100vh - 350px)">
                    <el-table-column prop="id" label="ID" width="60" align="center"></el-table-column>
                    <el-table-column prop="channel_account" label="渠道账号" align="center"
                        min-width="120"></el-table-column>
                    <el-table-column prop="maccount" label="用户账号" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="relation_id" label="订单ID" align="center" min-width="180"></el-table-column>
                    <el-table-column label="商品" align="center" min-width="100">
                        <template slot-scope="scope">
                            <div><span>{{ formatPayType(scope.row.pay_type) }}</span></div>
                            <div><span :style="{ color: '#409EFF', fontWeight: 'bold' }">{{
                                formatProductName(scope.row.product_id) }}</span></div>
                        </template>
                    </el-table-column>
                    <el-table-column label="支付金额" align="center" width="100">
                        <template slot-scope="scope">
                            <span :style="{ color: getAmountColor(scope.row.pay_amount), fontWeight: 'bold' }">¥{{
                                formatAmount(scope.row.pay_amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="返利金额" align="center" width="100">
                        <template slot-scope="scope">
                            <span :style="{ color: getAmountColor(scope.row.rebate_amount), fontWeight: 'bold' }">¥{{
                                formatAmount(scope.row.rebate_amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="返利类型" align="center" width="120">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                                <span :style="{ color: getStatusColor(scope.row.status), fontWeight: 'bold' }">{{
                                    formatStatus(scope.row.status) }}</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="提现状态" align="center" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.withdrew_status === 3 ? 'success' : (scope.row.withdrew_status === 2 ? 'info' : 'warning')">
                                <span
                                    :style="{ color: getWithdrewStatusColor(scope.row.withdrew_status), fontWeight: 'bold' }">{{
                                    formatWithdrewStatus(scope.row.withdrew_status) }}</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="rebate_day" label="预计返利日期" align="center" width="120"></el-table-column>
                    <el-table-column label="创建/更新时间" width="160" align="center">
                        <template slot-scope="scope">
                            <div>{{ scope.row.create_time }}</div>
                            <div>{{ scope.row.update_time }}</div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="pagination.page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
                        layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
                </div>
            </div>
        </el-card>
    </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
.date-separator {
    margin: 0 8px;
    color: #909399;
}
</style>