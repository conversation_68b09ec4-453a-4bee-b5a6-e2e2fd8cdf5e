import {login, logout as logout<PERSON>pi} from "@/api/user";
import {getToken, setToken, removeToken, getUserInfo, setUserInfo, removeUserInfo} from "@/utils/auth";
import router from "@/router";
import Layout from "@/layout";

const state = {
    token: getToken(),
    userInfo: getUserInfo(),
    permissionRoutes: [],
};

const mutations = {
    setToken: (state, token) => {
        state.token = token;
    },
    setUserInfo: (state, data) => {
        state.userInfo = data;
    },
    getPermissionRoutes: (state) => {
        // 递归处理菜单列表，生成路由配置
        function generateRoutes(menuList, parentPath = '') {
            const routes = []

            menuList.forEach(menu => {
                // 如果是目录类型
                if (menu.type === 1) {
                    const route = {
                        path: menu.show_action,
                        name: `${parentPath}${menu.show_action.replace(/\//g, '_')}`,
                        component: Layout,
                        meta: {
                            title: menu.title,
                            icon: menu.icon
                        }
                    }

                    // 如果有子菜单，递归处理
                    if (menu.menu && menu.menu.length > 0) {
                        // 如果只有一个子菜单，直接使用子菜单的配置
                        if (menu.menu.length === 1) {
                            const child = menu.menu[0]
                            route.path = child.show_action
                            route.name = `${parentPath}${child.show_action.replace(/\//g, '_')}`
                            route.children = [{
                                path: '',
                                name: `${parentPath}${child.show_action.replace(/\//g, '_')}_child`,
                                component: () => import(`@/views${child.show_action}.vue`),
                                meta: {
                                    title: child.title,
                                    icon: child.icon
                                }
                            }]
                            // 使用子菜单的标题和图标
                            route.meta = {
                                title: child.title,
                                icon: child.icon
                            }
                        } else {
                            route.children = generateRoutes(menu.menu, `${route.name}_`)
                        }
                    }
                    routes.push(route)
                }
                // 如果是页面类型
                else if (menu.type === 2) {
                    // 如果是顶级页面，需要包装在Layout中
                    if (!parentPath) {
                        const route = {
                            path: menu.show_action,
                            name: `${parentPath}${menu.show_action.replace(/\//g, '_')}`,
                            component: Layout,
                            meta: {
                                title: menu.title,
                                icon: menu.icon
                            },
                            children: [{
                                path: '',
                                name: `${parentPath}${menu.show_action.replace(/\//g, '_')}_child`,
                                component: () => import(`@/views${menu.show_action}.vue`),
                                meta: {
                                    title: menu.title,
                                    icon: menu.icon
                                }
                            }]
                        }
                        routes.push(route)
                    } else {
                        const route = {
                            path: menu.show_action,
                            name: `${parentPath}${menu.show_action.replace(/\//g, '_')}`,
                            component: () => import(`@/views${menu.show_action}.vue`),
                            meta: {
                                title: menu.title,
                                icon: menu.icon
                            }
                        }
                        routes.push(route)
                    }
                }
            })
            return routes
        }

        // 生成路由配置
        const routes = generateRoutes(state.userInfo.menuList)
        // console.log(routes)

        // 添加基础路由
        const baseRoutes = [
            {
                path: '/login',
                name: 'login',
                hidden: true,
                component: () => import("@/views/login"),
            },
            {
                path: '/404',
                name: '404',
                hidden: true,
                component: () => import("@/views/404.vue"),
            }
        ]

        // 合并路由
        state.permissionRoutes = [...baseRoutes, ...routes]
    },
};

const actions = {
    // user login
    login({commit}, req) {
        return new Promise((resolve, reject) => {
            login(req)
                .then((res) => {
                    let {data} = res;
                    commit("setToken", data.token);
                    setToken(data.token);
                    commit("setUserInfo", data);
                    setUserInfo(data);
                    resolve(res);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    },

    // user logout
    logout({commit}) {
        return new Promise(async (resolve, reject) => {
            try {
                // 先调用服务器端退出登录API
                await logoutApi();

                // 清除本地状态
                commit("setToken", null);
                removeToken();
                commit("setUserInfo", null);
                removeUserInfo();

                resolve();

                setTimeout(() => {
                    // 重定向到根路径（登录页面）
                    window.location.href = '/';
                }, 1000);
            } catch (error) {
                console.error('退出登录失败:', error);
                // 即使API调用失败，也要清除本地状态
                commit("setToken", null);
                removeToken();
                commit("setUserInfo", null);
                removeUserInfo();

                resolve(); // 仍然resolve，因为本地状态已清除

                setTimeout(() => {
                    // 重定向到根路径（登录页面）
                    window.location.href = '/';
                }, 1000);
            }
        });
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
};
