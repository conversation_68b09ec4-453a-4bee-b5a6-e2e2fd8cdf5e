{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@kangc/v-md-editor": "^1.7.12", "@modelcontextprotocol/server-sequential-thinking": "^0.6.2", "@vue/cli": "^4.5.0", "axios": "^1.6.2", "core-js": "^3.8.3", "echarts": "^5.5.1", "element-ui": "^2.15.14", "epic-spinners": "^1.1.0", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "or": "^0.2.0", "pikaz-excel-js": "^1.0.3", "vue": "^2.6.14", "vue-qr": "^4.0.9", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "sass": "^1.39.0", "sass-loader": "^12.0.0", "svg-sprite-loader": "^4.1.3", "svgo": "^1.2.0", "vue-template-compiler": "^2.6.14"}}