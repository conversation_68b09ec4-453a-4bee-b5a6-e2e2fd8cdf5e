<script>
import { server_list, server_edit, server_del, region_list, server_check, server_echarts } from '@/api/server';
import * as echarts from 'echarts';

export default {
  data() {
    return {
      servers: [],
      listLoading: false,
      dialogVisible: false,
      dialogStatus: 'create', // create或edit
      submitLoading: false,
      regionOptions: [],
      regionMap: {}, // 存储地区信息映射
      total: 0,
      currentPage: 1,
      pageSize: 20,
      // 密码可见性状态（使用普通对象以确保响应式）
      passwordVisible: {},
      searchForm: {
        region_id: '',
        status: '',
        nat_ip: '',
        proxy_type: '',
        account: ''
      },
      proxyTypes: [
        { value: 'http', label: 'HTTP' },
        { value: 'socks5', label: 'SOCKS5' },
        { value: 'https', label: 'HTTPS' }
      ],
      proxyMethods: [
        { value: 'aes-128-gcm', label: 'aes-128-gcm' },
        { value: 'aes-192-gcm', label: 'aes-192-gcm' },
        { value: 'aes-256-gcm', label: 'aes-256-gcm' },
        { value: 'aes-128-cfb', label: 'aes-128-cfb' },
        { value: 'aes-192-cfb', label: 'aes-192-cfb' },
        { value: 'aes-256-cfb', label: 'aes-256-cfb' },
        { value: 'rc4-md5', label: 'rc4-md5' },
        { value: 'chacha20', label: 'chacha20' }
      ],
      form: {
        server_id: 0,
        region_id: null,
        nat_ip: '',
        port: '',
        account: '',
        password: '',
        proxy_type: 'http',
        status: 1,
        guid: '',
        broadband: '',
        memory: ''
      },
      rules: {
        region_id: [
          { required: true, message: '请选择地区', trigger: 'change' }
        ],
        guid: [
          { required: true, message: '请输入GUID', trigger: 'blur' }
        ],
        nat_ip: [
          { required: true, message: '请输入IP/域名', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口', trigger: 'blur' }
        ],
        broadband: [
          { required: true, message: '请输入带宽(M)', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('带宽必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        memory: [
          { required: true, message: '请输入内存(M)', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error('内存必须大于0'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        proxy_type: [
          { required: true, message: '请选择代理类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      checkingId: null,
      ipInfoVisible: false,
      ipInfoData: null,
      weightInfoVisible: false,
      // 添加图表相关数据
      chartVisible: false,
      chartData: null,
      chartLoading: false,
      selectedServer: null
    };
  },
  created() {
    this.fetchServers();
    this.fetchRegionOptions();
  },
  methods: {
    // 切换密码显示状态
    togglePasswordVisibility(id) {
      console.log('切换密码显示状态，ID:', id);

      // 使用Vue的响应式方法设置状态
      this.$set(this.passwordVisible, id, !this.passwordVisible[id]);

      console.log('密码显示状态已切换，现在是否显示:', this.passwordVisible[id]);
    },

    // 获取服务器列表
    async fetchServers() {
      this.listLoading = true;
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          ...this.searchForm
        };

        const response = await server_list(params);
        if (response.code === 200) {
          // 获取服务器列表
          this.servers = response.data.lists;
          this.total = parseInt(response.data.total);
          this.currentPage = parseInt(response.data.current_page);

          // 重置密码可见性
          this.passwordVisible = {};
        }
      } catch (error) {
        console.error('Error fetching servers:', error);
      } finally {
        this.listLoading = false;
      }
    },

    // 获取地区选项
    async fetchRegionOptions() {
      try {
        const response = await region_list({});
        if (response.code === 200) {
          // 清空并重新构建regionMap
          this.regionMap = {};

          // 保存完整的地区数据到映射对象
          response.data.lists.forEach(item => {
            this.regionMap[item.id] = item;
          });

          // 设置下拉选项
          this.regionOptions = response.data.lists.map(item => ({
            value: item.id,
            label: `${item.regin_code}-${item.cn_name}-${item.regin_name}`
          }));
        }
      } catch (error) {
        console.error('Error fetching region options:', error);
      }
    },

    // 根据region_id获取地区名称
    getRegionName(regionId) {
      const region = this.regionOptions.find(r => r.value === regionId);
      return region ? region.label : '--';
    },

    // 根据region_id获取完整的地区信息
    getRegionInfo(regionId) {
      return this.regionMap[regionId] || null;
    },

    // 格式化代理类型
    formatProxyType(type) {
      const connType = this.proxyTypes.find(t => t.value === type);
      return connType ? connType.label : type;
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '--';
      return timeStr;
    },

    // 切换页码
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchServers();
    },

    // 切换每页数量
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.fetchServers();
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchServers();
    },

    // 重置搜索表单
    resetSearchForm() {
      this.searchForm = {
        region_id: '',
        status: '',
        nat_ip: '',
        proxy_type: '',
        account: ''
      };
      this.currentPage = 1;
      this.fetchServers();
    },

    // 重置表单
    resetForm() {
      this.form = {
        server_id: 0,
        region_id: null,
        nat_ip: '',
        port: '',
        account: '',
        password: '',
        proxy_type: 'http',
        status: 1,
        guid: '',
        broadband: '',
        memory: ''
      };
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },

    // 新增服务器
    handleAdd() {
      this.dialogStatus = 'create';
      this.resetForm();
      this.dialogVisible = true;
    },

    // 编辑服务器
    handleEdit(row) {
      this.dialogStatus = 'edit';
      this.resetForm();

      this.form = {
        server_id: row.id,
        region_id: row.region_id,
        nat_ip: row.nat_ip,
        port: row.port,
        account: row.account,
        password: row.password,
        proxy_type: row.proxy_type,
        status: row.status,
        guid: row.guid,
        broadband: row.broadband,
        memory: row.memory
      };
      this.dialogVisible = true;
    },

    // 提交表单
    async submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) {
          return false;
        }

        this.submitLoading = true;
        try {
          // 复制表单数据
          const params = { ...this.form };

          const response = await server_edit(params);
          if (response.code === 200) {
            this.dialogVisible = false;
            this.fetchServers(); // 重新获取列表
          }
        } catch (error) {
          console.error('Error submitting form:', error);
        } finally {
          this.submitLoading = false;
        }
      });
    },

    // 删除服务器
    async handleDelete(row) {
      this.$confirm('确认删除['+row.nat_ip+']该服务器?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await server_del({ server_id: row.id });
          if (response.code === 200) {
            this.fetchServers(); // 重新获取列表
          }
        } catch (error) {
          console.error('Error deleting server:', error);
        }
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 验证IP/域名
    async handleCheck(row) {
      this.checkingId = row.id;
      try {
        const response = await server_check({ server_id:row.id });
        if (response.code === 200) {
          console.log(response.data);
          this.ipInfoData = response.data;
          this.ipInfoVisible = true;
        }
      } catch (error) {
        console.error('Error checking server:', error);
      } finally {
        this.checkingId = null;
      }
    },

    // 显示预估权重信息
    showWeightInfo() {
      this.weightInfoVisible = true;
    },
    
    // 显示服务器性能图表
    async showChangesChart(row) {
      this.chartLoading = true;
      this.selectedServer = row;
      this.chartVisible = true;
      
      try {
        const response = await server_echarts({ sid: row.id });
        if (response.code === 200) {
          this.chartData = response.data;
          this.$nextTick(() => {
            this.initChart();
          });
        }
      } catch (error) {
        console.error('获取服务器性能数据失败:', error);
      } finally {
        this.chartLoading = false;
      }
    },
    
    // 初始化图表
    initChart() {
      if (!this.chartData) return;
      
      const chartDom = document.getElementById('performanceChart');
      const myChart = echarts.init(chartDom);
      
      // 格式化流量，将字节转换为MB
      const formatTraffic = (bytes) => {
        const mb = bytes / (1024 * 1024);
        return mb.toFixed(2);
      };
      
      // 将流量数据转换为MB
      const trafficData = this.chartData.datasets.traffic.map(value => formatTraffic(value));
      
      const option = {
        title: {
          text: '服务器性能监控'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            
            params.forEach(param => {
              const marker = param.marker;
              const seriesName = param.seriesName;
              const value = param.value;
              
              if (seriesName === '流量(MB)') {
                result += `${marker} ${seriesName}: ${value} MB<br/>`;
              } else {
                result += `${marker} ${seriesName}: ${value}%<br/>`;
              }
            });
            
            return result;
          }
        },
        legend: {
          data: ['流量(MB)', '内存使用率(%)', 'CPU使用率(%)']
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.chartData.labels
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '流量(MB)',
            position: 'left'
          },
          {
            type: 'value',
            name: '使用率(%)',
            position: 'right',
            min: 0,
            max: 100
          }
        ],
        series: [
          {
            name: '流量(MB)',
            type: 'line',
            smooth: true,
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: trafficData
          },
          {
            name: '内存使用率(%)',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.datasets.memory
          },
          {
            name: 'CPU使用率(%)',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.datasets.cpu
          }
        ]
      };
      
      myChart.setOption(option);
      
      // 窗口大小变化时重绘图表
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    }
  }
};
</script>

<template>
  <div class="app-container">
    <!-- 服务器管理卡片 -->
    <el-card shadow="hover" class="server-card">
      <div slot="header" class="clearfix">
        <span class="title-before">服务器管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">添加服务器</el-button>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="地区">
            <el-select v-model="searchForm.region_id" placeholder="请选择地区" clearable>
              <el-option
                v-for="item in regionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option :value="1" label="启用" />
              <el-option :value="2" label="禁用" />
              <el-option :value="3" label="离线" />
            </el-select>
          </el-form-item>
          <el-form-item label="IP/域名">
            <el-input v-model="searchForm.nat_ip" placeholder="请输入IP/域名" clearable></el-input>
          </el-form-item>
          <el-form-item label="代理类型">
            <el-select v-model="searchForm.proxy_type" placeholder="请选择代理类型" clearable>
              <el-option
                v-for="item in proxyTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="searchForm.account" placeholder="请输入用户名" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearchForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 服务器列表 -->
      <div class="list-section">
        <el-table
          v-loading="listLoading"
          :data="servers"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
          height="calc(100vh - 350px)"
        >
          <el-table-column prop="id" label="ID" align="center" width="70" />
          <el-table-column label="地区" align="center" width="160">
            <template slot-scope="scope">
              <world-icon
                v-if="getRegionInfo(scope.row.region_id)?.region_icon"
                :icon="`word${getRegionInfo(scope.row.region_id)?.region_icon}`"
                :size="24"
                class="region-icon"
              />
              {{ getRegionInfo(scope.row.region_id)?.cn_name || '--' }}
              <div>GUID:{{ scope.row.guid || '--' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="IP/域名" align="center" min-width="180">
            <template slot-scope="scope">
              <div>IP:{{ scope.row.nat_ip }}:{{ scope.row.port }}</div>
              <div class="ip-container">
                <span>Method:{{ formatProxyType(scope.row.proxy_type) }}</span>
                <el-button
                  type="text"
                  size="small"
                  class="edit-btn"
                  @click="handleCheck(scope.row)"
                >
                  验证
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="连接数/CPU" align="center" width="120">
            <template slot-scope="scope">
                <div>connect:{{ scope.row.connect_num || 0 }}</div>
                <div>CPU:{{ scope.row.cup || 0.00 }}%</div>
            </template>
          </el-table-column>
          <el-table-column label="带宽/内存(M)" align="center" width="120">
            <template slot-scope="scope">
                <div>{{ scope.row.broadband || 0 }}M / {{ scope.row.use_broadband || 0.00 }}%</div>
                <div>{{ scope.row.memory || 0 }}M / {{ scope.row.use_memory || 0.00 }}%</div>
            </template>
          </el-table-column>
          <el-table-column label="预估权重" align="center" width="180">
            <template slot="header">
              <div style="display: flex; align-items: center; justify-content: center;">
                <span>预估权重</span>
                <i class="el-icon-question" style="margin-left: 5px; cursor: pointer;" @click.stop="showWeightInfo"></i>
              </div>
            </template>
            <template slot-scope="scope">
              <div class="weight-container">
                <span>{{ scope.row.weight }}</span>
                <i class="el-icon-s-data chart-icon" @click.stop="showChangesChart(scope.row)" title="查看性能图表"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="用户名" align="center" width="100">
            <template slot-scope="scope">
              <div>ACC:{{ scope.row.account || '-' }}</div>
              <div>PWD:{{ scope.row.password || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 
               scope.row.status === 2 ? 'danger' : 
               scope.row.status === 3 ? 'info' : 'warning'">
              {{ scope.row.status === 1 ? '启用' : 
                scope.row.status === 2 ? '禁用' : 
                scope.row.status === 3 ? '离线' : '未知状态' }}
            </el-tag>

            </template>
          </el-table-column>
          <el-table-column label="心跳/创建时间" align="center" width="150">
            <template slot-scope="scope">
              {{ formatTime(scope.row.last_time) }}
              {{ formatTime(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" fixed="right" width="160">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleEdit(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增服务器' : '编辑服务器'"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" status-icon>
        <el-form-item label="所属地区" prop="region_id">
          <el-select
            v-model="form.region_id"
            filterable
            placeholder="请选择所属地区"
            :disabled="dialogStatus === 'edit'"
          >
            <el-option
              v-for="item in regionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="GUID" prop="guid">
          <el-input v-model="form.guid" placeholder="请输入GUID"></el-input>
        </el-form-item>
        <el-form-item label="IP/域名" prop="nat_ip">
          <el-input v-model="form.nat_ip" placeholder="请输入IP/域名"></el-input>
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入端口"></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="account">
          <el-input v-model="form.account" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="带宽(M)" prop="broadband">
          <el-input v-model="form.broadband" type="number" placeholder="请输入带宽">
            <template slot="append">M</template>
          </el-input>
        </el-form-item>
        <el-form-item label="内存(M)" prop="memory">
          <el-input v-model="form.memory" type="number" placeholder="请输入内存">
            <template slot="append">M</template>
          </el-input>
        </el-form-item>
        <el-form-item label="代理类型" prop="proxy_type">
          <el-select v-model="form.proxy_type" placeholder="请选择代理类型">
            <el-option
              v-for="item in proxyTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
            <el-radio :label="3">离线</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- IP验证信息弹窗 -->
    <el-dialog
      title="IP验证信息"
      :visible.sync="ipInfoVisible"
      width="500px"
      center
      :close-on-click-modal="false"
    >
      <div v-if="ipInfoData" class="ip-info">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="IP">{{ ipInfoData.ip }}</el-descriptions-item>
          <el-descriptions-item label="国家代码">{{ ipInfoData.country_code }}</el-descriptions-item>
          <el-descriptions-item label="国家/地区">{{ ipInfoData.country }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{ ipInfoData.city }}</el-descriptions-item>
          <el-descriptions-item label="区域">{{ ipInfoData.region }}</el-descriptions-item>
          <el-descriptions-item label="经纬度">{{ ipInfoData.latitude }}, {{ ipInfoData.longitude }}</el-descriptions-item>
          <el-descriptions-item label="组织">{{ ipInfoData.organization }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ipInfoVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 预估权重信息弹窗 -->
    <el-dialog
      title="预估权重计算公式"
      :visible.sync="weightInfoVisible"
      width="500px"
      center
      :close-on-click-modal="false"
    >
      <div class="weight-info">
        <p>预估权重 = 0.42 × (时间窗口流量 / 最大时间窗口流量)</p>
        <p>+ 0.25 × (时间窗口连接数 / 最大时间窗口连接数固定 1000)</p>  
        <p>+ 0.17 × (CPU使用率)</p>
        <p>+ 0.17 × (内存使用率)</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="weightInfoVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 服务器性能图表弹窗 -->
    <el-dialog
      title="服务器性能监控"
      :visible.sync="chartVisible"
      width="800px"
      center
      :before-close="() => chartVisible = false"
      :close-on-click-modal="false"
    >
      <div v-loading="chartLoading" class="chart-container">
        <div v-if="selectedServer" class="server-info">
          <p><strong>服务器:</strong> {{ selectedServer.nat_ip }} ({{ getRegionName(selectedServer.region_id) }})</p>
        </div>
        <div id="performanceChart" class="chart"></div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="chartVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.server-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  .title-before {
    position: relative;
    padding-left: 10px;
    font-size: 16px;
    font-weight: bold;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background-color: #409EFF;
    }
  }

  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.search-section {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .el-table {
    margin-bottom: 20px;
  }
}

.pagination-container {
  text-align: right;
}
.password-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.password-text {
  margin-right: 5px;
}
.password-toggle-btn {
  padding: 0;
  margin-left: 5px;
}
.password-toggle-btn i {
  font-size: 16px;
  color: #909399;
}
.password-toggle-btn:hover i {
  color: #409EFF;
}
.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
.ip-info {
  padding: 10px;
}
.weight-info {
  padding: 10px;
  line-height: 1.8;
  font-size: 14px;
  p {
    margin: 5px 0;
  }
}
.weight-container {
  display: flex;
  align-items: center;
  justify-content: center;

  .chart-icon {
    margin-left: 8px;
    color: #409EFF;
    cursor: pointer;
    font-size: 18px;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

.chart-container {
  min-height: 400px;
  
  .server-info {
    margin-bottom: 15px;
    
    p {
      margin: 0;
    }
  }
  
  .chart {
    width: 100%;
    height: 400px;
  }
}
</style>
