import request from '@/utils/request'


export function member_list(data) {
    return request({
        url: '/member/list',
        method: 'post',
        data
    })
}

export function member_edit(data) {
    return request({
        url: '/member/edit',
        method: 'post',
        data
    })
  }

export function member_del(data) {
    return request({
        url: '/member/del',
        method: 'post',
        data
    })
}
export function member_login_log(data) {
    return request({
        url: '/member/loginlog',
        method: 'post',
        data
    })
}

export function member_trial_log(data) {
    return request({
        url: '/member/triallog',
        method: 'post',
        data
    })
}

export function member_orders(data) {
    return request({
        url: '/member/orders',
        method: 'post',
        data
    })
}

export function member_order_ios(data) {
    return request({
        url: '/member/order_ios',
        method: 'post',
        data
    })
}

export function member_order_conf(data) {
    return request({
        url: '/member/order_conf',
        method: 'post',
        data
    })
}

export function home_data(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
export function home_echarts(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
