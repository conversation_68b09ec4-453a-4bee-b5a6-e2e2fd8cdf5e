<template>
  <div :id="id" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    id: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "200px",
    },
    height: {
      type: String,
      default: "200px",
    },
    chartData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id));

      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          formatter: (params) => {
            let tip = "";
            if (params != null && params.length > 0) {
              tip += params[0].name + "<br />";
              for (let i = 0; i < params.length; i++) {
                let value = params[i].value;
                tip += params[i].marker + params[i].seriesName + "：" + (value ? value + this.chartData.unit : "-") + "<br />";
              }
            }
            return tip;
          },
        },
        legend: {
          data: this.chartData.legend,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: this.chartData.xAxis,
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: this.chartData.series,
      });

      this.chart.resize();
    },
  },
};
</script>
