import md5 from "js-md5";
import { rsaEncrypt, rsaDecrypt } from "@/utils/rsa";

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

export function copyToClip(text) {
  return new Promise((resolve, reject) => {
    try {
      const input = document.createElement("textarea");
      input.setAttribute("readonly", "readonly");
      input.value = text;
      document.body.appendChild(input);
      input.select();
      if (document.execCommand("copy")) document.execCommand("copy");
      document.body.removeChild(input);
      resolve(text);
    } catch (error) {
      reject(error);
    }
  });
}

// 深拷贝
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

// 格式化时间
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

// 是否JSON字符串

export function isJsonString(str) {
  try {
    if (typeof JSON.parse(str) == "object") {
      return true;
    }
  } catch (e) {}
  return false;
}

// 全局参数校验
export function globalParams(obj) {
  // let socket_file
  // if(obj.hasOwnProperty('socket_file')){
  //   socket_file = obj.socket_file
  //   delete obj.socket_file
  // }
  obj.t = Date.now().toString();
  let requestData = {
    s: "",
  };
  let SIGNKEY = `cy7F8SyTxQDO92aHHFlElSWoSQ38nHte`;
  let sign = "";
  let arr = [];

  Object.keys(obj)
    .sort()
    .forEach((item) => {
      requestData[item] = obj[item];
      arr.push(obj[item]);
    });
  sign = arr.join("");
  requestData.s = md5(sign + SIGNKEY);
  // if(!!socket_file){
  //   requestData.socket_file = socket_file
  // }
  return requestData;
}

/** 图片压缩，默认同比例压缩
 *  @param {Object} fileObj
 *  图片对象
 *  回调函数有一个参数，base64的字符串数据
 */
export function compress(fileObj, callback) {
  try {
    const image = new Image();
    image.src = URL.createObjectURL(fileObj);
    image.onload = function () {
      const that = this;
      // 默认按比例压缩
      let w = that.width;
      let h = that.height;
      const scale = w / h;
      w = fileObj.width || w;
      h = fileObj.height || w / scale;
      let quality = 0.7; // 默认图片质量为0.7
      // 生成canvas
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      // 创建属性节点
      const anw = document.createAttribute("width");
      anw.nodeValue = w;
      const anh = document.createAttribute("height");
      anh.nodeValue = h;
      canvas.setAttributeNode(anw);
      canvas.setAttributeNode(anh);
      ctx.drawImage(that, 0, 0, w, h);
      // 图像质量
      if (fileObj.quality && fileObj.quality <= 1 && fileObj.quality > 0) {
        quality = fileObj.quality;
      }
      // quality值越小，所绘制出的图像越模糊
      const data = canvas.toDataURL("image/jpeg", quality);
      // 压缩完成执行回调
      const newFile = convertBase64UrlToBlob(data);
      callback(newFile);
    };
  } catch (e) {
    console.log("压缩失败!");
  }
}

export function compressFile(fileObj){
  return new Promise((resolve,reject) => {
    try{
      const image = new Image();
    image.src = URL.createObjectURL(fileObj);
    image.onload = function () {
      const that = this;
      // 默认按比例压缩
      let w = that.width;
      let h = that.height;
      const scale = w / h;
      w = fileObj.width || w;
      h = fileObj.height || w / scale;
      let quality = 0.7; // 默认图片质量为0.7
      // 生成canvas
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      // 创建属性节点
      const anw = document.createAttribute("width");
      anw.nodeValue = w;
      const anh = document.createAttribute("height");
      anh.nodeValue = h;
      canvas.setAttributeNode(anw);
      canvas.setAttributeNode(anh);
      ctx.drawImage(that, 0, 0, w, h);
      // 图像质量
      if (fileObj.quality && fileObj.quality <= 1 && fileObj.quality > 0) {
        quality = fileObj.quality;
      }
      // quality值越小，所绘制出的图像越模糊
      const data = canvas.toDataURL("image/jpeg", quality);
      // 压缩完成执行回调
      const newFile = convertBase64UrlToBlob(data);
      resolve(newFile)
    };
    }catch(err){
      reject(err)
    }
  })
}
function convertBase64UrlToBlob(urlData) {
  const bytes = window.atob(urlData.split(",")[1]); // 去掉url的头，并转换为byte
  // 处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], { type: "image/png" });
}

export function convertImgToBase64(url) {
  return new Promise((resolve, reject) => {
    if (!url) {
      reject('请传入url内容');
    }

    if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(url)) {
      // 图片地址
      const image = new Image();
      // 设置跨域问题
      image.setAttribute('crossOrigin', 'Anonymous');
      // 图片地址
      image.src = url;
      image.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = image.width;
        canvas.height = image.height;
        ctx.drawImage(image, 0, 0, image.width, image.height);
        // 获取图片后缀
        const ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
        // 转base64
        const dataUrl = canvas.toDataURL(`image/${ext}`);
        resolve(dataUrl || '');
      }
    } else {
      // 非图片地址
      reject('非(png/jpe?g/gif/svg等)图片地址');
    }
  });
}

export function getFileFromUrl(url) {
  return new Promise((resolve, reject) => {
      var blob = null;
      var xhr = new XMLHttpRequest();
      xhr.open("GET", url);
      xhr.setRequestHeader('Accept', 'image/png');
      xhr.responseType = "blob";
      // 加载时处理
      xhr.onload = () => {
        // 获取返回结果
          blob = xhr.response;
          let file= new File([blob], 'txt2img', { type: 'image/png' });
          // 返回结果
          resolve(file);
      };
      xhr.onerror = (e) => {
          reject(e)
      };
      // 发送
      xhr.send();
  });
}

export function blobToBase64(blob){
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.addEventListener('load', ()=> {
        resolve(reader.result)
    });
    reader.readAsDataURL(blob);
  })
}


/**
 * @param {number} bytes
 * @param {boolean} [hasUnits=true]
 * @return {string}
 */
export function formatBytes(bytes, hasUnits = true) {
  if (!bytes) {
      return "--";
  }
  // 定义单位
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  // 如果字节数小于1KB，用0.xx KB表示
  if (bytes < 1024) {
      return (bytes / 1024).toFixed(2) + (hasUnits ? 'KB' : '');
  }
  // 计算单位
  const exponent = Math.floor(Math.log(bytes) / Math.log(1024));
  const unit = units[exponent];
  const value = bytes / Math.pow(1024, exponent);
  // 根据不同的单位格式化输出
  switch (unit) {
      case 'KB':
          return value.toFixed(2) + (hasUnits ? 'K' : '');
      case 'MB':
          return value.toFixed(2) + (hasUnits ? 'M' : '');
      case 'GB':
          return value.toFixed(2) + (hasUnits ? 'G' : '');
      case 'TB':
          return value.toFixed(2) + (hasUnits ? 'T' : '');
      default:
          return bytes + (hasUnits ? 'B' : '');
  }
}

export function getBytesGB(bytes){
  const exponent = Math.floor(Math.log(bytes) / Math.log(1024));
  const value = bytes / Math.pow(1024, exponent);
  return value.toFixed(2)
}

/**
 * Generates a random password.
 * @param {number} length - The length of the password.
 * @return {string} The generated password.
 */
export function randomPassword(length = 12) {
  const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialChars = '#$%&*-';
  let password = letters.charAt(Math.floor(Math.random() * letters.length));
  password += numbers.charAt(Math.floor(Math.random() * numbers.length));
  password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));
  const allChars = letters + numbers + specialChars;
  for (let i = 3; i < length; i++) {
      password += allChars.charAt(Math.floor(Math.random() * allChars.length));
  }
  return password.split('').sort(() => 0.5 - Math.random()).join('');
}
