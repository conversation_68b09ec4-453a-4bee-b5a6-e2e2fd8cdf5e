<script setup>
import { ref, reactive, onMounted } from 'vue'
import { channel_withdrew_list, channel_slist } from '@/api/channel'
import CheckWithdrew from '@/components/Channel/checkWithdrew.vue'

// 定义搜索表单数据
const searchForm = reactive({
  channel_id: '',
  status: '',
  ct_start: '',
  ct_end: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPage: 0
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 查看详情对话框
const checkWithdrewRef = ref(null)

// 图片预览弹窗
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 渠道选项
const channelOptions = ref([])

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '审核中', value: '1' },
  { label: '审核不通过', value: '2' },
  { label: '已打款', value: '3' }
]

// 获取渠道列表
const getChannelList = async () => {
  try {
    const res = await channel_slist({})
    if (res.code === 200) {
      channelOptions.value = [
        { label: '全部', value: '' },
        ...res.data.map(item => ({
          label: item.account,
          value: item.id
        }))
      ]
    }
  } catch (error) {
    console.error('获取渠道列表失败', error)
  }
}

// 获取提现列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const res = await channel_withdrew_list(params)
    if (res.code === 200) {
      tableData.value = res.data.lists
      pagination.total = res.data.total
      pagination.totalPage = res.data.total_page
    }
  } catch (error) {
    console.error('获取提现列表失败', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  getList()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.page = val
  getList()
}

// 格式化金额（分转元）
const formatAmount = (amount) => {
  if (!amount || amount === 0) return '0.00'
  const yuan = parseFloat(amount) / 100
  return yuan.toFixed(2)
}

// 获取金额颜色
const getAmountColor = (amount) => {
  const yuan = parseFloat(amount) / 100 || 0
  if (yuan >= 1000) {
    return '#67C23A' // 绿色 - 高金额 (>=1000元)
  } else if (yuan >= 500) {
    return '#E6A23C' // 橙色 - 中等金额 (500-999元)
  } else if (yuan > 0) {
    return '#409EFF' // 蓝色 - 低金额 (>0元)
  } else {
    return '#909399' // 灰色 - 无金额 (0元)
  }
}

// 格式化状态
const formatStatus = (status) => {
  switch (status) {
    case 1:
      return '未审核'
    case 2:
      return '审核不通过'
    case 3:
      return '已打款'
    default:
      return '未知'
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1:
      return '#E6A23C' // 橙色 - 审核中
    case 2:
      return '#F56C6C' // 红色 - 审核不通过
    case 3:
      return '#67C23A' // 绿色 - 已打款
    default:
      return '#909399' // 灰色 - 未知
  }
}

// 查看图片 - 在本页面显示
const viewImage = (imageUrl) => {
  if (imageUrl) {
    previewImageUrl.value = imageUrl
    imagePreviewVisible.value = true
  }
}

// 关闭图片预览
const closeImagePreview = () => {
  imagePreviewVisible.value = false
  previewImageUrl.value = ''
}

// 查看详情
const handleView = (row) => {
  // 使用 ref 调用组件的 open 方法，传递更多信息
  if (checkWithdrewRef.value) {
    checkWithdrewRef.value.open(row.id, row.channel_account, row.amount)
  } else {
    console.error('checkWithdrewRef is null')
  }
}

// 页面加载时获取数据
onMounted(() => {
  getChannelList()
  getList()
})
</script>

<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 页面标题 -->
      <div slot="header" class="clearfix">
        <span class="card-title">提现审核管理</span>
      </div>

      <!-- 搜索表单 -->
      <div class="search-section">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="渠道">
            <el-select v-model="searchForm.channel_id" placeholder="请选择渠道" clearable>
              <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker v-model="searchForm.ct_start" type="date" placeholder="开始日期" value-format="yyyy-MM-dd"
              style="width: 160px;"></el-date-picker>
            <span class="date-separator">至</span>
            <el-date-picker v-model="searchForm.ct_end" type="date" placeholder="结束日期" value-format="yyyy-MM-dd"
              style="width: 160px;"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 提现列表 -->
      <div class="list-section">
        <el-table v-loading="loading" :data="tableData" element-loading-text="加载中..." border fit highlight-current-row
          style="width: 100%" height="calc(100vh - 350px)">
          <el-table-column prop="id" label="ID" width="60" align="center"></el-table-column>
          <el-table-column prop="channel_account" label="渠道账号" align="center" min-width="120"></el-table-column>
          <el-table-column label="提现金额" align="center" width="120">
            <template slot-scope="scope">
              <span :style="{ color: getAmountColor(scope.row.amount), fontWeight: 'bold' }">¥{{
                formatAmount(scope.row.amount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="120">
            <template slot-scope="scope">
              <span :style="{ color: getStatusColor(scope.row.status), fontWeight: 'bold' }">
                {{ formatStatus(scope.row.status) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="关联订单数" align="center" width="100">
            <template slot-scope="scope">{{ scope.row.order_count || 0 }} 条</template>
          </el-table-column>
          <el-table-column label="备注" align="center" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.reason" class="remark-content">
                <el-tooltip :content="scope.row.reason" placement="top" :disabled="scope.row.reason.length <= 20">
                  <span class="remark-text">{{ scope.row.reason.length > 20 ? scope.row.reason.substring(0, 20) + '...'
                    : scope.row.reason }}</span>
                </el-tooltip>
              </div>
              <span v-else class="no-remark">-</span>
            </template>
          </el-table-column>
          <el-table-column label="打款凭据" align="center" width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.pic_url">
                <el-button size="mini" type="text" icon="el-icon-view"
                  @click="viewImage(scope.row.pic_url)">查看</el-button>
              </div>
              <el-tag v-else size="mini" type="info">暂无</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center" width="160"></el-table-column>
          <el-table-column label="操作" align="center" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="handleView(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pagination.page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>
      </div>
    </el-card>

    <!-- 查看详情弹窗 -->
    <CheckWithdrew ref="checkWithdrewRef" @refresh="getList" />

    <!-- 图片预览弹窗 -->
    <el-dialog title="查看凭据" :visible.sync="imagePreviewVisible" @close="closeImagePreview" width="80%"
      :close-on-click-modal="true" :append-to-body="true" center>
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="打款凭据" class="preview-image" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeImagePreview">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
.date-separator {
  margin: 0 8px;
  color: #909399;
}

// 备注内容样式
.remark-content {
  .remark-text {
    color: #606266;
    font-size: 13px;
    line-height: 1.4;
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }
}

.no-remark {
  color: #c0c4cc;
  font-style: italic;
}

// 图片预览样式
.image-preview-container {
  text-align: center;
  padding: 20px;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.dialog-footer {
  text-align: right;
}
</style>