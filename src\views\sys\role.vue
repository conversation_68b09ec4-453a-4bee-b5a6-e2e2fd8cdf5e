<template>
  <div class="app-container">
    <!-- 角色管理卡片 -->
    <el-card shadow="hover" class="page-card">
      <div slot="header" class="card-header">
        <span class="title-before">角色管理</span>
        <div class="header-buttons">
          <el-button type="text" @click="handleAddRole">新增角色</el-button>
        </div>
      </div>

      <!-- 角色列表 -->
      <div class="list-section">
        <el-table
          v-loading="loading"
          :data="roleList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column prop="role_id" label="角色ID" width="100" align="center"></el-table-column>
          <el-table-column prop="role_name" label="角色名称" width="150" align="center"></el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.disable === 1 ? 'success' : 'danger'">
                {{ scope.row.disable === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="update_time" label="更新时间" align="center"></el-table-column>
          <el-table-column label="操作" width="280" fixed="right" align="center">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="handleEditRole(scope.row)">编辑</el-button>
              <el-button type="warning" size="mini" @click="handleToggleStatus(scope.row)">
                {{ scope.row.disable === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button type="success" size="mini" @click="handlePermissionChange(scope.row)">权限变更</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 新增角色对话框 -->
    <el-dialog
      title="新增角色"
      :visible.sync="addDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入角色名称"></el-input>
        </el-form-item>
        <el-form-item label="菜单权限" prop="role_menu">
          <el-tree
            ref="addMenuTree"
            :data="menuList"
            show-checkbox
            node-key="mid"
            :props="{ children: 'menu', label: 'title' }"
            :default-checked-keys="[]"
            :check-strictly="false"
            @check="handleAddMenuCheck"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑角色对话框 -->
    <el-dialog
      title="编辑角色"
      :visible.sync="editDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
        status-icon
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入角色名称"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="disable">
          <el-radio-group v-model="editForm.disable">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限" prop="role_menu">
          <el-tree
            ref="editMenuTree"
            :data="menuList"
            show-checkbox
            node-key="mid"
            :props="{ children: 'menu', label: 'title' }"
            :default-checked-keys="[]"
            :check-strictly="false"
            @check="handleEditMenuCheck"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 权限变更对话框 -->
    <el-dialog
      title="权限变更"
      :visible.sync="permissionDialogVisible"
      width="600px"
    >
      <el-tree
        ref="permissionTree"
        :data="menuList"
        show-checkbox
        node-key="mid"
        :props="{ children: 'menu', label: 'title' }"
        :default-checked-keys="[]"
        :check-strictly="false"
        @check="handlePermissionCheck"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPermissionForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { role_list, role_add, role_edit, role_menu, menu_list } from '@/api/sys'

// 获取Vue实例
const { proxy } = getCurrentInstance()

// 定义表格数据
const roleList = ref([])
const menuList = ref([])
const loading = ref(false)

// 新增角色对话框
const addDialogVisible = ref(false)
const addForm = reactive({
  name: '',
  role_menu: ''
})

// 新增表单验证规则
const addFormRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
}

// 新增表单ref
const addFormRef = ref(null)
const addMenuTree = ref(null)

// 编辑角色对话框
const editDialogVisible = ref(false)
const editForm = reactive({
  rid: '',
  name: '',
  disable: 1,
  role_menu: ''
})

// 编辑表单验证规则
const editFormRules = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
}

// 编辑表单ref
const editFormRef = ref(null)
const editMenuTree = ref(null)

// 权限变更对话框
const permissionDialogVisible = ref(false)
const permissionTree = ref(null)
const currentRoleId = ref(null)
const selectedPermissions = ref([])

// 获取角色列表数据
const getList = async () => {
  loading.value = true
  try {
    const res = await role_list()
    roleList.value = res.data.lists
  } catch (error) {
    console.error('获取角色列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取菜单列表数据
const getMenuList = async () => {
  try {
    const res = await menu_list()
    menuList.value = res.data.lists
  } catch (error) {
    console.error('获取菜单列表失败:', error)
  }
}

// 打开添加角色对话框
const handleAddRole = async () => {
  addDialogVisible.value = true
  await getMenuList()
  // 重置表单
  Object.keys(addForm).forEach(key => {
    addForm[key] = ''
  })
}

// 打开编辑角色对话框
const handleEditRole = async (role) => {
  editDialogVisible.value = true
  await getMenuList()
  // 填充表单数据
  editForm.rid = role.role_id || ''
  editForm.name = role.role_name || ''
  editForm.disable = role.disable || 1
  editForm.role_menu = ''

  await getRoleMenu(role.role_id)
}

// 打开权限变更对话框
const handlePermissionChange = async (role) => {
  currentRoleId.value = role.role_id
  permissionDialogVisible.value = true
  await getMenuList()
  await getRoleMenu(role.role_id)
}

// 获取角色菜单权限
const getRoleMenu = async (roleId) => {
  try {
    const res = await role_menu({ rid: roleId })
    const selectedMenus = getSelectedMenus(res.data.lists)
    if (editDialogVisible.value) {
      nextTick(() => {
        setCheckedNodes(editMenuTree.value, selectedMenus)
      })
      editForm.role_menu = selectedMenus.join(',')
    } else if (permissionDialogVisible.value) {
      nextTick(() => {
        setCheckedNodes(permissionTree.value, selectedMenus)
      })
      selectedPermissions.value = selectedMenus
    }
  } catch (error) {
    console.error('获取角色菜单失败:', error)
  }
}

// 获取选中的菜单
const getSelectedMenus = (menuList) => {
  const selectedMenus = []
  const traverse = (menus) => {
    menus.forEach(menu => {
      if (menu.selected === 1) {
        selectedMenus.push(menu.mid)
      }
      if (menu.menu && menu.menu.length > 0) {
        traverse(menu.menu)
      }
    })
  }
  traverse(menuList)
  return selectedMenus
}

// 设置树节点选中状态
const setCheckedNodes = (tree, checkedKeys) => {
  if (!tree) return
  // 重置所有节点的选中状态
  tree.setCheckedKeys([])

  // 设置选中状态
  checkedKeys.forEach(key => {
    const node = tree.getNode(key)
    if (node) {
      tree.setChecked(node, true, false)
    }
  })
}

// 处理新增菜单选择
const handleAddMenuCheck = (data, checked) => {
  if (addMenuTree.value) {
    addForm.role_menu = addMenuTree.value.getCheckedKeys(true).join(',')
  }
}

// 处理编辑菜单选择
const handleEditMenuCheck = (data, checked) => {
  if (editMenuTree.value) {
    const checkedKeys = editMenuTree.value.getCheckedKeys()
    const halfCheckedKeys = editMenuTree.value.getHalfCheckedKeys()
    editForm.role_menu = [...checkedKeys, ...halfCheckedKeys].join(',')
  }
}

// 处理权限变更菜单选择
const handlePermissionCheck = (data, checked) => {
  if (permissionTree.value) {
    selectedPermissions.value = permissionTree.value.getCheckedKeys(true)
  }
}

// 提交新增表单
const submitAddForm = async () => {
  if (!addFormRef.value) return

  addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await role_add(addForm)
        addDialogVisible.value = false
        getList() // 刷新列表
      } catch (error) {
        console.error('新增角色失败:', error)
      }
    }
  })
}

// 提交编辑表单
const submitEditForm = async () => {
  if (!editFormRef.value) return

  editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await role_edit(editForm)
        editDialogVisible.value = false
        getList() // 刷新列表
      } catch (error) {
        console.error('编辑角色失败:', error)
      }
    }
  })
}

// 提交权限变更
const submitPermissionForm = async () => {
  try {
    await role_edit({
      rid: currentRoleId.value,
      role_menu: selectedPermissions.value.join(',')
    })
    permissionDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('权限变更失败:', error)
  }
}

// 切换角色状态
const handleToggleStatus = async (role) => {
  try {
    await role_edit({
      rid: role.role_id,
      disable: role.disable === 1 ? 2 : 1
    })
    getList() // 刷新列表
  } catch (error) {
    console.error('切换角色状态失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
// 注意：大部分通用样式已移动到 src/styles/common-views.scss 中
// 这里只保留页面特有的样式
</style>
