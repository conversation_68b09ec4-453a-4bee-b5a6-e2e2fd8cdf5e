import request from '@/utils/request'


export function channel_list(data) {
    return request({
        url: '/channel/list',
        method: 'post',
        data
    })
}

export function channel_slist(data) {
    return request({
        url: '/channel/slist',
        method: 'post',
        data
    })
}

export function channel_edit(data) {
    return request({
        url: '/channel/edit',
        method: 'post',
        data
    })
}

export function channel_code_list(data) {
    return request({
        url: '/channel/code_list',
        method: 'post',
        data
    })
}

export function channel_code_edit(data) {
    return request({
        url: '/channel/code_edit',
        method: 'post',
        data
    })
}

export function channel_login_log(data) {
    return request({
        url: '/channel/login_log',
        method: 'post',
        data
    })
}

export function channel_rebate(data) {
    return request({
        url: '/channel/rebate',
        method: 'post',
        data
    })
}

// 返利订单提现接口
export function channel_withdrew_list(data) {
  return request({
    url: "/channel/withdrew_list",
    method: "post",
    data,
  });
}

// 返利订单提现接口
export function channel_withdrew_info(data) {
  return request({
    url: "/channel/withdrew_info",
    method: "post",
    data,
  });
}

// 返利订单提现接口
export function channel_check_withdrew(data) {
  return request({
    url: "/channel/check_withdrew",
    method: "post",
    data,
  });
}
