export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

// 邮箱验证
export function validEmail(value) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(value)
}

// 手机号
export function validMobile(mobile){
  const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/
  return reg.test(mobile)
}

export function validatePassword(password, username) {
  // 确保密码长度至少为6个字符
  const lengthCheck = /.{6,}/;
  
  // 检查是否包含三类字符中的至少三类
  const complexityCheck = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])|(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])|(?=.*[a-z])(?=.*[0-9])(?=.*[^a-zA-Z0-9])|(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9])/;

  // 检查是否包含用户名
  if (username && password.includes(username)) {
      return false;
  }

  // 检查是否包含超过两个连续字符的姓名部分
  if (username) {
      const nameParts = username.split('');
      for (let i = 0; i < nameParts.length - 2; i++) {
          const substring = username.substring(i, i + 3);
          if (password.includes(substring)) {
              return false;
          }
      }
  }

  // 执行正则检查
  return lengthCheck.test(password) && complexityCheck.test(password);
}

export function validateIP(ip) {
  const reg = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/
  return reg.test(ip)
}